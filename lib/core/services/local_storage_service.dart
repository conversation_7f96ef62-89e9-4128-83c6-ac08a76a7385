import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// Service for handling local storage operations
class LocalStorageService {
  static const String _isPremiumKey = 'is_premium';
  static SharedPreferences? _prefs;

  /// Initialize the local storage service
  static Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      AppLogger.info('Local storage service initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize local storage service', e);
    }
  }

  /// Save premium status to local storage
  static Future<bool> savePremiumStatus(bool isPremium) async {
    try {
      if (_prefs == null) {
        await initialize();
      }
      
      final result = await _prefs!.setBool(_isPremiumKey, isPremium);
      AppLogger.info('Premium status saved to local storage: $isPremium');
      return result;
    } catch (e) {
      AppLogger.error('Failed to save premium status to local storage', e);
      return false;
    }
  }

  /// Get premium status from local storage
  /// Returns false if not found or on error
  static bool getPremiumStatus() {
    try {
      if (_prefs == null) {
        AppLogger.warning('Local storage not initialized when getting premium status');
        return false;
      }
      
      final isPremium = _prefs!.getBool(_isPremiumKey) ?? false;
      AppLogger.info('Premium status retrieved from local storage: $isPremium');
      return isPremium;
    } catch (e) {
      AppLogger.error('Failed to get premium status from local storage', e);
      return false;
    }
  }

  /// Clear all data from local storage
  static Future<bool> clearAll() async {
    try {
      if (_prefs == null) {
        await initialize();
      }
      
      final result = await _prefs!.clear();
      AppLogger.info('Local storage cleared');
      return result;
    } catch (e) {
      AppLogger.error('Failed to clear local storage', e);
      return false;
    }
  }
}
