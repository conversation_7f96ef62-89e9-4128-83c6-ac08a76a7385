import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'custom_app_icon_service.dart';

/// Service to handle dynamic app icon changes based on premium status
class AppIconService {

  /// Initialize the app icon service
  /// This should be called once at app startup
  static Future<void> initialize() async {
    if (Platform.isAndroid) {
      try {
        await CustomAppIconService.initialize();
        debugPrint('App icon service initialized');
      } catch (e) {
        debugPrint('Failed to initialize app icon service: $e');
      }
    }
  }

  /// Check if the device supports changing app icons
  static Future<bool> get supportsAlternateIcons async {
    try {
      if (Platform.isIOS) {
        debugPrint('iOS dynamic icon functionality not implemented');
        return false;
      } else if (Platform.isAndroid) {
        // Android implementation always returns true since we can't easily check
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error checking if device supports alternate icons: $e');
      return false;
    }
  }

  /// Set the app icon based on premium status
  static Future<bool> setAppIcon(bool isPremium) async {
    debugPrint('Setting app icon for premium status: $isPremium');
    try {
      if (Platform.isIOS) {
        debugPrint('iOS dynamic icon functionality not implemented');
        return false;
      } else if (Platform.isAndroid) {
        return await CustomAppIconService.setAppIcon(isPremium);
      }
      return false;
    } on PlatformException catch (e) {
      debugPrint('Platform exception changing app icon: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('Error changing app icon: $e');
      return false;
    }
  }

  /// Reset the app icon to the default
  static Future<bool> resetAppIcon() async {
    debugPrint('Resetting app icon to default');
    try {
      if (Platform.isIOS) {
        debugPrint('iOS dynamic icon functionality not implemented');
        return false;
      } else if (Platform.isAndroid) {
        // Use regular icon as default
        return await CustomAppIconService.setAppIcon(false);
      }
      return false;
    } on PlatformException catch (e) {
      debugPrint('Platform exception resetting app icon: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('Error resetting app icon: $e');
      return false;
    }
  }
}
