import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../services/app_icon_service.dart';
import '../services/local_storage_service.dart';
import '../../features/user/domain/usecases/user_service.dart';

/// Provider for managing app theme based on user's premium status
class ThemeProvider extends ChangeNotifier {
  final UserService _userService;
  bool _isPremium = false;
  bool _isLoading = true;

  ThemeProvider(this._userService) {
    // Load premium status from local storage first
    _isPremium = LocalStorageService.getPremiumStatus();

    // Set app icon based on premium status from local storage
    // This ensures the app icon is set as early as possible
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AppIconService.setAppIcon(_isPremium);
    });

    // Then load from Firebase
    _loadPremiumStatus();
  }

  /// Whether the user is a premium user
  bool get isPremium => _isPremium;

  /// Whether the theme is still loading
  bool get isLoading => _isLoading;

  /// Get the current theme data based on premium status
  ThemeData get currentTheme => _isPremium ? AppTheme.premiumTheme : AppTheme.lightTheme;

  /// Get the appropriate app bar based on premium status
  PreferredSizeWidget gradientAppBar({
    required String title,
    List<Widget>? actions,
    bool centerTitle = true,
    PreferredSizeWidget? bottom,
    double elevation = 0,
  }) {
    if (_isPremium) {
      return AppTheme.premiumGoldAppBar(
        title: title,
        actions: actions,
        centerTitle: centerTitle,
        bottom: bottom,
        elevation: elevation,
      );
    } else {
      return AppTheme.gradientAppBar(
        title: title,
        actions: actions,
        centerTitle: centerTitle,
        bottom: bottom,
        elevation: elevation,
      );
    }
  }

  /// Get the appropriate card decoration based on premium status
  BoxDecoration get cardDecoration {
    if (_isPremium) {
      return AppTheme.premiumGoldBorderedCardDecoration;
    } else {
      return AppTheme.cardDecoration;
    }
  }

  /// Get the appropriate fancy card decoration based on premium status
  BoxDecoration get fancyCardDecoration {
    if (_isPremium) {
      return AppTheme.premiumGoldCardDecoration;
    } else {
      return AppTheme.fancyCardDecoration;
    }
  }

  /// Load the premium status from the user service
  Future<void> _loadPremiumStatus() async {
    _isLoading = true;
    notifyListeners();

    try {
      final bool wasPremium = _isPremium;
      final bool firebasePremium = await _userService.isPremiumUser();

      // Update premium status if it changed
      if (wasPremium != firebasePremium) {
        _isPremium = firebasePremium;

        // Save to local storage
        await LocalStorageService.savePremiumStatus(_isPremium);

        // Update app icon
        debugPrint('Premium status changed from $wasPremium to $_isPremium, updating app icon');
        final bool success = await AppIconService.setAppIcon(_isPremium);
        debugPrint('App icon update ${success ? 'succeeded' : 'failed'}');
      }
    } catch (e) {
      debugPrint('Error loading premium status from Firebase: $e');
      // Keep the value from local storage if there's an error
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Update the premium status
  Future<void> updatePremiumStatus(bool isPremium) async {
    if (_isPremium == isPremium) return;

    _isLoading = true;
    notifyListeners();

    try {
      // Update in Firebase
      await _userService.updatePremiumStatus(isPremium);

      // Store the old premium status for logging
      final bool oldStatus = _isPremium;
      _isPremium = isPremium;

      // Save to local storage
      await LocalStorageService.savePremiumStatus(_isPremium);

      // Update app icon based on new premium status
      debugPrint('Premium status updated from $oldStatus to $_isPremium, updating app icon');
      final bool success = await AppIconService.setAppIcon(_isPremium);
      debugPrint('App icon update ${success ? 'succeeded' : 'failed'}');
    } catch (e) {
      debugPrint('Error updating premium status: $e');
      // Keep the current status if update fails
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Refresh the premium status from the user service
  Future<void> refreshPremiumStatus() async {
    await _loadPremiumStatus();
  }
}
