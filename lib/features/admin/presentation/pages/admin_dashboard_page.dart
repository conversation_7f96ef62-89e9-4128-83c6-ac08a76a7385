import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/widgets/fancy_card.dart';
import '../../domain/entities/dashboard_stats.dart';
import '../../domain/usecases/admin_service.dart';
import '../widgets/dashboard_card.dart';
import 'user_management_page.dart';
import 'content_management_with_upload_page.dart';
import 'analytics_page.dart';

class AdminDashboardPage extends StatefulWidget {
  const AdminDashboardPage({super.key});

  @override
  State<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends State<AdminDashboardPage> {
  DashboardStats? _stats;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadDashboardStats();
  }

  Future<void> _loadDashboardStats() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final adminService = Provider.of<AdminService>(context, listen: false);
      final stats = await adminService.getDashboardStats();

      setState(() {
        _stats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Admin Dashboard',
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: isPremium ? AppTheme.premiumGold : Colors.white,
            ),
            onPressed: _loadDashboardStats,
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: Icon(
              Icons.admin_panel_settings,
              color: isPremium ? AppTheme.premiumGold : Colors.white,
            ),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Admin Settings - Coming Soon')),
              );
            },
            tooltip: 'Admin Settings',
          ),
        ],
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppTheme.errorRed,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading dashboard',
              style: AppTheme.headingMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.secondaryText),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadDashboardStats,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDashboardStats,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWelcomeSection(),
            const SizedBox(height: 24),
            _buildStatsGrid(),
            const SizedBox(height: 24),
            _buildQuickActions(),
            const SizedBox(height: 24),
            _buildRecentActivity(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return FancyCard(
      gradient: isPremium
          ? AppTheme.premiumGoldGradient
          : AppTheme.primaryGradient,
      padding: const EdgeInsets.all(20),
      margin: EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome to Admin Dashboard',
                  style: AppTheme.headingLarge.copyWith(
                    color: isPremium ? AppTheme.premiumBlack : Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Manage your QuickPosters app efficiently',
                  style: AppTheme.bodyMedium.copyWith(
                    color: isPremium
                        ? AppTheme.premiumBlack.withValues(alpha: 0.7)
                        : Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isPremium
                  ? AppTheme.premiumBlack.withValues(alpha: 0.1)
                  : Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.admin_panel_settings,
              size: 36,
              color: isPremium ? AppTheme.premiumBlack : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    if (_stats == null) return const SizedBox.shrink();

    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine grid layout based on screen width
        int crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;
        double childAspectRatio = constraints.maxWidth > 600 ? 1.3 : 1.1;

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: childAspectRatio,
          children: [
            DashboardCard(
              title: 'Total Users',
              value: _stats!.totalUsers.toString(),
              icon: Icons.people,
              color: AppTheme.primaryBlue,
              subtitle: '${_stats!.newUsersToday} new today',
            ),
            DashboardCard(
              title: 'Premium Users',
              value: _stats!.premiumUsers.toString(),
              icon: Icons.star,
              color: AppTheme.premiumGold,
              subtitle: _stats!.totalUsers > 0
                  ? '${((_stats!.premiumUsers / _stats!.totalUsers) * 100).toStringAsFixed(1)}% of total'
                  : '0% of total',
            ),
            DashboardCard(
              title: 'Templates',
              value: _stats!.totalTemplates.toString(),
              icon: Icons.image,
              color: AppTheme.successGreen,
              subtitle: 'Available templates',
            ),
            DashboardCard(
              title: 'Banners',
              value: _stats!.totalBanners.toString(),
              icon: Icons.flag,
              color: AppTheme.warningOrange,
              subtitle: 'Active banners',
            ),
            DashboardCard(
              title: 'Business Users',
              value: _stats!.businessUsers.toString(),
              icon: Icons.business,
              color: AppTheme.primaryBlue,
              subtitle: 'Business accounts',
            ),
            DashboardCard(
              title: 'Politicians',
              value: _stats!.politicianUsers.toString(),
              icon: Icons.account_balance,
              color: AppTheme.errorRed,
              subtitle: 'Political accounts',
            ),
          ],
        );
      },
    );
  }

  Widget _buildQuickActions() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headingMedium.copyWith(
            color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            // Responsive grid for quick actions
            int crossAxisCount = constraints.maxWidth > 600 ? 4 : 2;
            double childAspectRatio = constraints.maxWidth > 600 ? 2.0 : 2.2;

            return GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: childAspectRatio,
              children: [
            _buildActionCard(
              title: 'Manage Users',
              icon: Icons.people_outline,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const UserManagementPage()),
              ),
            ),
            _buildActionCard(
              title: 'Content Management',
              icon: Icons.folder_outlined,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const ContentManagementWithUploadPage()),
              ),
            ),
            _buildActionCard(
              title: 'Analytics',
              icon: Icons.analytics_outlined,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const AnalyticsPage()),
              ),
            ),
            _buildActionCard(
              title: 'System Settings',
              icon: Icons.settings_outlined,
              onTap: () {
                // TODO: Navigate to system settings
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('System Settings - Coming Soon')),
                );
              },
            ),
          ],
        );
      },
    ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return FancyCard(
      onTap: onTap,
      margin: EdgeInsets.zero,
      padding: const EdgeInsets.all(16),
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isPremium
                  ? AppTheme.premiumGold.withValues(alpha: 0.2)
                  : AppTheme.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: AppTheme.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
              ),
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: AppTheme.headingMedium.copyWith(
            color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        FancyCard(
          margin: EdgeInsets.zero,
          backgroundColor: isPremium ? AppTheme.premiumDarkGrey : AppTheme.backgroundWhite,
          child: Column(
            children: [
              _buildActivityItem(
                icon: Icons.person_add,
                title: 'New user registered',
                subtitle: '2 minutes ago',
                color: AppTheme.successGreen,
              ),
              Divider(color: isPremium ? AppTheme.premiumLightGrey : AppTheme.lightGray),
              _buildActivityItem(
                icon: Icons.image,
                title: 'Template uploaded',
                subtitle: '15 minutes ago',
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
              Divider(color: isPremium ? AppTheme.premiumLightGrey : AppTheme.lightGray),
              _buildActivityItem(
                icon: Icons.star,
                title: 'User upgraded to premium',
                subtitle: '1 hour ago',
                color: AppTheme.premiumGold,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTheme.bodyMedium.copyWith(
                    color: isPremium ? AppTheme.premiumLightGold : AppTheme.textRichBlack,
                  ),
                ),
                Text(
                  subtitle,
                  style: AppTheme.bodySmall.copyWith(
                    color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
