import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'business_profile.dart';
import 'political_profile.dart';

/// Model class representing a user in the application
class UserModel extends Equatable {
  /// Unique identifier for the user (Firebase UID)
  final String uid;

  /// User's display name
  final String name;

  /// User's email address
  final String? email;

  /// User's phone number
  final String? phoneNumber;

  /// URL to the user's profile photo
  final String? photoUrl;

  /// When the user account was created
  final DateTime createdAt;

  /// When the user profile was last updated
  final DateTime updatedAt;

  /// Whether the user has completed their profile setup
  final bool isProfileComplete;

  /// User's preferred language code (e.g., 'en', 'hi', 'mr')
  final String languageCode;

  /// List of poster IDs created by this user
  final List<String> posterIds;

  /// User type (politician, businessman, or regular user)
  final String? userType;

  /// Whether the user is a premium user
  final bool isPremium;

  /// Whether the user has admin privileges
  final bool isAdmin;

  /// Business profile for businessman user type
  final BusinessProfile? businessProfile;

  /// Political profile for politician user type
  final PoliticalProfile? politicalProfile;

  const UserModel({
    required this.uid,
    required this.name,
    this.email,
    this.phoneNumber,
    this.photoUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isProfileComplete = false,
    this.languageCode = 'en',
    this.posterIds = const [],
    this.userType,
    this.isPremium = false,
    this.isAdmin = false,
    this.businessProfile,
    this.politicalProfile,
  });

  /// Create a new user with default values
  factory UserModel.create({
    required String uid,
    required String name,
    String? email,
    String? phoneNumber,
    String? photoUrl,
    String languageCode = 'en',
    String? userType,
    bool isPremium = false,
    bool isAdmin = false,
    BusinessProfile? businessProfile,
    PoliticalProfile? politicalProfile,
  }) {
    final now = DateTime.now();
    return UserModel(
      uid: uid,
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      photoUrl: photoUrl,
      createdAt: now,
      updatedAt: now,
      isProfileComplete: false,
      languageCode: languageCode,
      posterIds: [],
      userType: userType,
      isPremium: isPremium,
      isAdmin: isAdmin,
      businessProfile: businessProfile,
      politicalProfile: politicalProfile,
    );
  }

  /// Create a user model from Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Parse business profile if it exists
    BusinessProfile? businessProfile;
    if (data['businessProfile'] != null) {
      businessProfile = BusinessProfile.fromMap(data['businessProfile']);
    }

    // Parse political profile if it exists
    PoliticalProfile? politicalProfile;
    if (data['politicalProfile'] != null) {
      politicalProfile = PoliticalProfile.fromMap(data['politicalProfile']);
    }

    return UserModel(
      uid: doc.id,
      name: data['name'] ?? '',
      email: data['email'],
      phoneNumber: data['phoneNumber'],
      photoUrl: data['photoUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      isProfileComplete: data['isProfileComplete'] ?? false,
      languageCode: data['languageCode'] ?? 'en',
      posterIds: List<String>.from(data['posterIds'] ?? []),
      userType: data['userType'],
      isPremium: data['isPremium'] ?? false,
      isAdmin: data['isAdmin'] ?? false,
      businessProfile: businessProfile,
      politicalProfile: politicalProfile,
    );
  }

  /// Convert user model to a map for Firestore
  Map<String, dynamic> toFirestore() {
    final Map<String, dynamic> data = {
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'photoUrl': photoUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isProfileComplete': isProfileComplete,
      'languageCode': languageCode,
      'posterIds': posterIds,
      'userType': userType,
      'isPremium': isPremium,
      'isAdmin': isAdmin,
    };

    // Add business profile if it exists
    if (businessProfile != null) {
      data['businessProfile'] = businessProfile!.toMap();
    }

    // Add political profile if it exists
    if (politicalProfile != null) {
      data['politicalProfile'] = politicalProfile!.toMap();
    }

    return data;
  }

  /// Create a copy of this user with the given fields replaced with new values
  UserModel copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    String? photoUrl,
    DateTime? updatedAt,
    bool? isProfileComplete,
    String? languageCode,
    List<String>? posterIds,
    String? userType,
    bool? isPremium,
    bool? isAdmin,
    BusinessProfile? businessProfile,
    PoliticalProfile? politicalProfile,
  }) {
    return UserModel(
      uid: uid,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      photoUrl: photoUrl ?? this.photoUrl,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      languageCode: languageCode ?? this.languageCode,
      posterIds: posterIds ?? this.posterIds,
      userType: userType ?? this.userType,
      isPremium: isPremium ?? this.isPremium,
      isAdmin: isAdmin ?? this.isAdmin,
      businessProfile: businessProfile ?? this.businessProfile,
      politicalProfile: politicalProfile ?? this.politicalProfile,
    );
  }

  @override
  List<Object?> get props => [
        uid,
        name,
        email,
        phoneNumber,
        photoUrl,
        createdAt,
        updatedAt,
        isProfileComplete,
        languageCode,
        posterIds,
        userType,
        isPremium,
        isAdmin,
        businessProfile,
        politicalProfile,
      ];
}
