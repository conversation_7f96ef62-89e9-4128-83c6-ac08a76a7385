import '../entities/user_model.dart';
import '../entities/business_profile.dart';
import '../entities/political_profile.dart';
import '../repositories/user_repository.dart';

/// Service class for user-related operations
class UserService {
  final UserRepository _userRepository;

  UserService(this._userRepository);

  /// Create a new user in the database
  Future<void> createUser({
    required String uid,
    required String name,
    String? email,
    String? phoneNumber,
    String? photoUrl,
    String languageCode = 'en',
  }) async {
    final user = UserModel.create(
      uid: uid,
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      photoUrl: photoUrl,
      languageCode: languageCode,
    );

    await _userRepository.createUser(user);
  }

  /// Get a user by their UID
  Future<UserModel?> getUserById(String uid) async {
    return await _userRepository.getUserById(uid);
  }

  /// Update an existing user's information
  Future<void> updateUser(UserModel user) async {
    await _userRepository.updateUser(user);
  }

  /// Update specific fields of a user
  Future<void> updateUserFields({
    required String uid,
    String? name,
    String? email,
    String? phoneNumber,
    String? photoUrl,
    bool? isProfileComplete,
    String? languageCode,
    List<String>? posterIds,
    String? userType,
    bool? isPremium,
    bool? isAdmin,
  }) async {
    final user = await _userRepository.getUserById(uid);
    if (user == null) {
      throw Exception('User not found');
    }

    final updatedUser = user.copyWith(
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      photoUrl: photoUrl,
      isProfileComplete: isProfileComplete,
      languageCode: languageCode,
      posterIds: posterIds,
      userType: userType,
      isPremium: isPremium,
      isAdmin: isAdmin,
      updatedAt: DateTime.now(),
    );

    await _userRepository.updateUser(updatedUser);
  }

  /// Delete a user from the database
  Future<void> deleteUser(String uid) async {
    await _userRepository.deleteUser(uid);
  }

  /// Check if a user exists in the database
  Future<bool> userExists(String uid) async {
    return await _userRepository.userExists(uid);
  }

  /// Get the current authenticated user
  Future<UserModel?> getCurrentUser() async {
    return await _userRepository.getCurrentUser();
  }

  /// Upload a profile photo for a user
  Future<String?> uploadProfilePhoto(String uid, String localFilePath) async {
    return await _userRepository.uploadProfilePhoto(uid, localFilePath);
  }

  /// Add a poster ID to a user's list of posters
  Future<void> addPosterToUser(String uid, String posterId) async {
    final user = await _userRepository.getUserById(uid);
    if (user == null) {
      throw Exception('User not found');
    }

    if (!user.posterIds.contains(posterId)) {
      final updatedPosterIds = List<String>.from(user.posterIds)..add(posterId);
      final updatedUser = user.copyWith(
        posterIds: updatedPosterIds,
        updatedAt: DateTime.now(),
      );

      await _userRepository.updateUser(updatedUser);
    }
  }

  /// Remove a poster ID from a user's list of posters
  Future<void> removePosterFromUser(String uid, String posterId) async {
    final user = await _userRepository.getUserById(uid);
    if (user == null) {
      throw Exception('User not found');
    }

    if (user.posterIds.contains(posterId)) {
      final updatedPosterIds = List<String>.from(user.posterIds)..remove(posterId);
      final updatedUser = user.copyWith(
        posterIds: updatedPosterIds,
        updatedAt: DateTime.now(),
      );

      await _userRepository.updateUser(updatedUser);
    }
  }

  /// Update the user type for a user
  Future<void> updateUserType(String userType) async {
    final user = await getCurrentUser();
    if (user == null) {
      throw Exception('Current user not found');
    }

    final updatedUser = user.copyWith(
      userType: userType,
      updatedAt: DateTime.now(),
    );

    await _userRepository.updateUser(updatedUser);
  }

  /// Check if the user has selected a user type
  Future<bool> hasUserType() async {
    final user = await getCurrentUser();
    return user?.userType != null;
  }

  /// Check if the user is a premium user
  Future<bool> isPremiumUser() async {
    final user = await getCurrentUser();
    return user?.isPremium ?? false;
  }

  /// Update the premium status of the current user
  Future<void> updatePremiumStatus(bool isPremium) async {
    final user = await getCurrentUser();
    if (user == null) {
      throw Exception('Current user not found');
    }

    final updatedUser = user.copyWith(
      isPremium: isPremium,
      updatedAt: DateTime.now(),
    );

    await _userRepository.updateUser(updatedUser);
  }

  /// Update the business profile for the current user
  Future<void> updateBusinessProfile({
    String? businessName,
    String? businessType,
    String? address,
    String? mobileNumber,
    String? website,
    String? logoUrl,
    Map<String, String>? socialMediaProfiles,
  }) async {
    final user = await getCurrentUser();
    if (user == null) {
      throw Exception('Current user not found');
    }

    // Create or update the business profile
    final currentProfile = user.businessProfile ?? const BusinessProfile();
    final updatedProfile = currentProfile.copyWith(
      businessName: businessName,
      businessType: businessType,
      address: address,
      mobileNumber: mobileNumber,
      website: website,
      logoUrl: logoUrl,
      socialMediaProfiles: socialMediaProfiles,
    );

    // Update the user with the new business profile
    final updatedUser = user.copyWith(
      businessProfile: updatedProfile,
      updatedAt: DateTime.now(),
    );

    await _userRepository.updateUser(updatedUser);
  }

  /// Upload a business logo for the current user
  Future<String?> uploadBusinessLogo(String localFilePath) async {
    final user = await getCurrentUser();
    if (user == null) {
      throw Exception('Current user not found');
    }

    // Upload the logo to storage
    final logoUrl = await _userRepository.uploadBusinessLogo(
      user.uid,
      localFilePath,
    );

    // Update the business profile with the new logo URL
    if (logoUrl != null) {
      await updateBusinessProfile(logoUrl: logoUrl);
    }

    return logoUrl;
  }

  /// Get the business profile for the current user
  Future<BusinessProfile?> getBusinessProfile() async {
    final user = await getCurrentUser();
    return user?.businessProfile;
  }

  /// Update the political profile for the current user
  Future<void> updatePoliticalProfile({
    Map<String, dynamic>? parameterValues,
  }) async {
    final user = await getCurrentUser();
    if (user == null) {
      throw Exception('Current user not found');
    }

    // Create or update the political profile
    final currentProfile = user.politicalProfile ?? const PoliticalProfile();

    // If we're updating specific parameters, merge them with existing ones
    Map<String, dynamic>? updatedValues;
    if (parameterValues != null) {
      updatedValues = Map<String, dynamic>.from(currentProfile.parameterValues ?? {});
      updatedValues.addAll(parameterValues);
    }

    final updatedProfile = currentProfile.copyWith(
      parameterValues: updatedValues,
    );

    // Update the user with the new political profile
    final updatedUser = user.copyWith(
      politicalProfile: updatedProfile,
      updatedAt: DateTime.now(),
    );

    await _userRepository.updateUser(updatedUser);
  }

  /// Get the political profile for the current user
  Future<PoliticalProfile?> getPoliticalProfile() async {
    final user = await getCurrentUser();
    return user?.politicalProfile;
  }

  /// Upload a political photo for the current user
  Future<String?> uploadPoliticalPhoto(String localFilePath) async {
    final user = await getCurrentUser();
    if (user == null) {
      throw Exception('Current user not found');
    }

    // Upload the photo to storage
    final photoUrl = await _userRepository.uploadPoliticalPhoto(
      user.uid,
      localFilePath,
    );

    // Update the political profile with the new photo URL
    if (photoUrl != null) {
      await updatePoliticalProfile(
        parameterValues: {'political_photo': photoUrl},
      );
    }

    return photoUrl;
  }

  /// Check if the current user is an admin
  Future<bool> isCurrentUserAdmin() async {
    final user = await getCurrentUser();
    return user?.isAdmin ?? false;
  }

  /// Update admin status for a user
  Future<void> updateAdminStatus(String uid, bool isAdmin) async {
    await updateUserFields(uid: uid, isAdmin: isAdmin);
  }

  /// Check if a specific user is an admin
  Future<bool> isUserAdmin(String uid) async {
    final user = await _userRepository.getUserById(uid);
    return user?.isAdmin ?? false;
  }
}
