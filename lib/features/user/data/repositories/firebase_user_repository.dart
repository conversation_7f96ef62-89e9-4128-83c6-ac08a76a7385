import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../domain/entities/user_model.dart';
import '../../domain/repositories/user_repository.dart';

/// Implementation of UserRepository using Firebase services
class FirebaseUserRepository implements UserRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final FirebaseStorage _storage;

  /// Collection reference for users
  late final CollectionReference _usersCollection;

  FirebaseUserRepository({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    FirebaseStorage? storage,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance,
        _storage = storage ?? FirebaseStorage.instance {
    _usersCollection = _firestore.collection('users');
  }

  @override
  Future<void> createUser(UserModel user) async {
    try {
      await _usersCollection.doc(user.uid).set(user.toFirestore());
    } catch (e) {
      throw Exception('Failed to create user: $e');
    }
  }

  @override
  Future<UserModel?> getUserById(String uid) async {
    try {
      final docSnapshot = await _usersCollection.doc(uid).get();
      if (docSnapshot.exists) {
        return UserModel.fromFirestore(docSnapshot);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  @override
  Future<void> updateUser(UserModel user) async {
    try {
      // Update the updatedAt timestamp
      final updatedUser = user.copyWith(updatedAt: DateTime.now());
      await _usersCollection.doc(user.uid).update(updatedUser.toFirestore());
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  @override
  Future<void> deleteUser(String uid) async {
    try {
      await _usersCollection.doc(uid).delete();
    } catch (e) {
      throw Exception('Failed to delete user: $e');
    }
  }

  @override
  Future<bool> userExists(String uid) async {
    try {
      final docSnapshot = await _usersCollection.doc(uid).get();
      return docSnapshot.exists;
    } catch (e) {
      throw Exception('Failed to check if user exists: $e');
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return null;
      }

      return await getUserById(currentUser.uid);
    } catch (e) {
      throw Exception('Failed to get current user: $e');
    }
  }

  @override
  Future<String?> uploadProfilePhoto(String uid, String localFilePath) async {
    try {
      final file = File(localFilePath);
      final storageRef = _storage.ref().child('profile_photos/$uid.jpg');

      // Upload the file
      await storageRef.putFile(
        file,
        SettableMetadata(contentType: 'image/jpeg'),
      );

      // Get the download URL
      final downloadUrl = await storageRef.getDownloadURL();

      // Update the user's photoUrl
      final user = await getUserById(uid);
      if (user != null) {
        final updatedUser = user.copyWith(
          photoUrl: downloadUrl,
          updatedAt: DateTime.now(),
        );
        await updateUser(updatedUser);
      }

      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload profile photo: $e');
    }
  }

  @override
  Future<String?> uploadBusinessLogo(String uid, String localFilePath) async {
    try {
      // Create a file reference
      final file = File(localFilePath);
      if (!file.existsSync()) {
        throw Exception('File does not exist');
      }

      // Create a storage reference
      final storageRef = _storage
          .ref()
          .child('users')
          .child(uid)
          .child('business')
          .child('logo.jpg');

      // Upload the file
      await storageRef.putFile(
        file,
        SettableMetadata(contentType: 'image/jpeg'),
      );

      // Get the download URL
      final downloadUrl = await storageRef.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload business logo: $e');
    }
  }

  @override
  Future<String?> uploadPoliticalPhoto(String uid, String localFilePath) async {
    try {
      // Create a file reference
      final file = File(localFilePath);
      if (!file.existsSync()) {
        throw Exception('File does not exist');
      }

      // Create a storage reference
      final storageRef = _storage
          .ref()
          .child('users')
          .child(uid)
          .child('political')
          .child('photo.jpg');

      // Upload the file
      await storageRef.putFile(
        file,
        SettableMetadata(contentType: 'image/jpeg'),
      );

      // Get the download URL
      final downloadUrl = await storageRef.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload political photo: $e');
    }
  }
}
