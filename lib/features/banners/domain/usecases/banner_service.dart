import '../entities/banner_item.dart';
import '../../data/repositories/banner_repository.dart';

/// Service for managing banner-related operations
class BannerService {
  final BannerRepository _bannerRepository;

  BannerService(this._bannerRepository);

  /// Reset pagination state
  void resetPagination() {
    _bannerRepository.resetPagination();
  }

  /// Check if more banners are available
  bool get hasMoreBanners => _bannerRepository.hasMoreBanners;

  /// Get banners with pagination
  Future<List<BannerItem>> getBanners({int limit = 5}) async {
    final List<String> imageUrls = await _bannerRepository.getBannerImages(limit: limit);

    // Convert URLs to BannerItem objects
    return imageUrls.map((url) => BannerItem.fromStorageUrl(url)).toList();
  }

  /// Get all available banners (legacy method, use getBanners with pagination instead)
  Future<List<BannerItem>> getAllBanners() async {
    // Reset pagination to ensure we get all banners from the beginning
    resetPagination();

    // Get first batch of banners
    List<BannerItem> allBanners = await getBanners(limit: 100);

    return allBanners;
  }

  /// Get active banners only
  Future<List<BannerItem>> getActiveBanners() async {
    final List<BannerItem> allBanners = await getAllBanners();

    // Filter out inactive banners
    return allBanners.where((banner) => banner.isActive).toList();
  }
}
