import 'package:flutter/material.dart';
import '../entities/subscription_plan.dart';
import '../../../user/domain/usecases/user_service.dart';
import '../../../../core/utils/logger.dart';

/// Service for handling subscription-related operations
class SubscriptionService {
  final UserService _userService;
  
  /// Constructor
  SubscriptionService(this._userService);
  
  /// Get all available subscription plans
  List<SubscriptionPlan> getAvailablePlans() {
    return SubscriptionPlan.predefinedPlans;
  }
  
  /// Get the recommended subscription plan
  SubscriptionPlan? getRecommendedPlan() {
    final plans = getAvailablePlans();
    try {
      return plans.firstWhere((plan) => plan.isRecommended);
    } catch (e) {
      // If no plan is marked as recommended, return the first one
      return plans.isNotEmpty ? plans.first : null;
    }
  }
  
  /// Check if the user is currently subscribed
  Future<bool> isUserSubscribed() async {
    try {
      return await _userService.isPremiumUser();
    } catch (e) {
      AppLogger.error('Error checking subscription status', e);
      return false;
    }
  }
  
  /// Process a subscription purchase
  /// 
  /// This is a placeholder for actual payment processing logic
  /// In a real app, this would integrate with a payment gateway
  Future<bool> purchaseSubscription(SubscriptionPlan plan, BuildContext context) async {
    try {
      // Show a loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
      
      // Simulate payment processing delay
      await Future.delayed(const Duration(seconds: 2));
      
      // Close the loading indicator
      Navigator.of(context).pop();
      
      // Update the user's premium status
      await _userService.updatePremiumStatus(true);
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Successfully subscribed to ${plan.name}!'),
          backgroundColor: Colors.green,
        ),
      );
      
      return true;
    } catch (e) {
      // Close the loading indicator if it's still showing
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      
      AppLogger.error('Error processing subscription purchase', e);
      
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process payment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      return false;
    }
  }
  
  /// Cancel the current subscription
  /// 
  /// This is a placeholder for actual subscription cancellation logic
  Future<bool> cancelSubscription(BuildContext context) async {
    try {
      // Show a loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
      
      // Simulate cancellation processing delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Close the loading indicator
      Navigator.of(context).pop();
      
      // Update the user's premium status
      await _userService.updatePremiumStatus(false);
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Subscription successfully cancelled'),
          backgroundColor: Colors.green,
        ),
      );
      
      return true;
    } catch (e) {
      // Close the loading indicator if it's still showing
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      
      AppLogger.error('Error cancelling subscription', e);
      
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel subscription: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      return false;
    }
  }
}
