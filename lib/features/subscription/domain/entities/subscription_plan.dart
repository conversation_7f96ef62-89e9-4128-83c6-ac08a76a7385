/// Represents a subscription plan in the app
class SubscriptionPlan {
  /// Unique identifier for the plan
  final String id;
  
  /// Display name of the plan
  final String name;
  
  /// Description of the plan
  final String description;
  
  /// Price in INR
  final double price;
  
  /// Duration in days
  final int durationDays;
  
  /// Whether this plan has a trial period
  final bool hasTrial;
  
  /// Trial duration in days (if applicable)
  final int? trialDays;
  
  /// Price after trial period (if applicable)
  final double? priceAfterTrial;
  
  /// Duration after trial period in days (if applicable)
  final int? durationAfterTrialDays;
  
  /// Features included in this plan
  final List<String> features;
  
  /// Whether this plan is recommended
  final bool isRecommended;
  
  /// Constructor
  const SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.durationDays,
    this.hasTrial = false,
    this.trialDays,
    this.priceAfterTrial,
    this.durationAfterTrialDays,
    required this.features,
    this.isRecommended = false,
  });
  
  /// Get the formatted price string (e.g., "₹199")
  String get formattedPrice => '₹${price.toInt()}';
  
  /// Get the formatted duration string (e.g., "3 months", "1 year")
  String get formattedDuration {
    if (durationDays >= 365) {
      final years = durationDays ~/ 365;
      return '$years ${years == 1 ? 'year' : 'years'}';
    } else if (durationDays >= 30) {
      final months = durationDays ~/ 30;
      return '$months ${months == 1 ? 'month' : 'months'}';
    } else {
      return '$durationDays ${durationDays == 1 ? 'day' : 'days'}';
    }
  }
  
  /// Get the formatted trial duration string (e.g., "8 days")
  String? get formattedTrialDuration {
    if (!hasTrial || trialDays == null) return null;
    
    return '$trialDays ${trialDays == 1 ? 'day' : 'days'}';
  }
  
  /// Get the formatted price after trial string (e.g., "₹199")
  String? get formattedPriceAfterTrial {
    if (!hasTrial || priceAfterTrial == null) return null;
    
    return '₹${priceAfterTrial!.toInt()}';
  }
  
  /// Get the formatted duration after trial string (e.g., "3 months")
  String? get formattedDurationAfterTrial {
    if (!hasTrial || durationAfterTrialDays == null) return null;
    
    if (durationAfterTrialDays! >= 365) {
      final years = durationAfterTrialDays! ~/ 365;
      return '$years ${years == 1 ? 'year' : 'years'}';
    } else if (durationAfterTrialDays! >= 30) {
      final months = durationAfterTrialDays! ~/ 30;
      return '$months ${months == 1 ? 'month' : 'months'}';
    } else {
      return '$durationAfterTrialDays ${durationAfterTrialDays == 1 ? 'day' : 'days'}';
    }
  }
  
  /// Calculate the effective price per day
  double get pricePerDay {
    if (hasTrial && trialDays != null && priceAfterTrial != null && durationAfterTrialDays != null) {
      // For trial plans, calculate the effective price including the trial period
      return (price + priceAfterTrial!) / (trialDays! + durationAfterTrialDays!);
    } else {
      return price / durationDays;
    }
  }
  
  /// Calculate savings percentage compared to monthly pricing (assuming monthly is ₹199 for 3 months)
  double get savingsPercentage {
    // Base monthly price (₹199 for 3 months)
    const double monthlyPriceFor3Months = 199.0;
    const int monthlyDurationDays = 90; // 3 months
    
    // Calculate the equivalent cost at monthly pricing
    final double equivalentMonthlyPrice = (durationDays / monthlyDurationDays) * monthlyPriceFor3Months;
    
    // Calculate savings
    final double savings = equivalentMonthlyPrice - price;
    
    // Calculate savings percentage
    return (savings / equivalentMonthlyPrice) * 100;
  }
  
  /// Predefined subscription plans
  static List<SubscriptionPlan> get predefinedPlans => [
    // 8-day trial plan
    const SubscriptionPlan(
      id: 'trial',
      name: 'Trial Plan',
      description: 'Try Premium features for 8 days at a minimal cost',
      price: 9,
      durationDays: 8,
      hasTrial: true,
      trialDays: 8,
      priceAfterTrial: 199,
      durationAfterTrialDays: 90, // 3 months
      features: [
        'All Premium features',
        'Auto-renews at ₹199 for 3 months',
        'Cancel anytime',
      ],
    ),
    
    // 1-year plan
    const SubscriptionPlan(
      id: 'annual',
      name: 'Annual Plan',
      description: 'Best value for regular users',
      price: 499,
      durationDays: 365, // 1 year
      features: [
        'All Premium features',
        'Save 58% compared to quarterly plan',
        'Priority support',
      ],
      isRecommended: true,
    ),
    
    // 3-year plan
    const SubscriptionPlan(
      id: 'three_year',
      name: '3-Year Plan',
      description: 'Ultimate savings for long-term commitment',
      price: 999,
      durationDays: 1095, // 3 years
      features: [
        'All Premium features',
        'Save 72% compared to quarterly plan',
        'Priority support',
        'Early access to new features',
      ],
    ),
  ];
}
