import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../domain/usecases/subscription_service.dart';

/// Marketing page for the Premium trial offer
class PremiumTrialPage extends StatefulWidget {
  const PremiumTrialPage({super.key});

  @override
  State<PremiumTrialPage> createState() => _PremiumTrialPageState();
}

class _PremiumTrialPageState extends State<PremiumTrialPage> {
  bool _isLoading = false;
  late SubscriptionPlan _trialPlan;

  @override
  void initState() {
    super.initState();
    // Get the trial plan
    _trialPlan = SubscriptionPlan.predefinedPlans.firstWhere(
      (plan) => plan.id == 'trial',
      orElse: () => SubscriptionPlan.predefinedPlans.first,
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black,
              Colors.black.withAlpha(204), // 0.8 * 255 = 204
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Close button
              Positioned(
                top: 16,
                right: 16,
                child: IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 28,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),

              // Content
              SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),

                      // Premium logo and title
                      Image.asset(
                        'assets/icons/premium_logo.png',
                        height: 80,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'PREMIUM',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.premiumGold,
                          letterSpacing: 2.0,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Unlock the full potential of QuickPosters',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                        ),
                        textAlign: TextAlign.center,
                      ),

                      // Trial offer
                      Container(
                        margin: const EdgeInsets.symmetric(vertical: 24),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(128), // 0.5 * 255 = 128
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppTheme.premiumGold.withAlpha(128), // 0.5 * 255 = 128
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'Special Offer',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.premiumGold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '₹',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.premiumGold,
                                  ),
                                ),
                                Text(
                                  '${_trialPlan.price.toInt()}',
                                  style: TextStyle(
                                    fontSize: 48,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.premiumGold,
                                  ),
                                ),
                              ],
                            ),
                            Text(
                              'for ${_trialPlan.formattedTrialDuration}',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Then ₹${_trialPlan.priceAfterTrial?.toInt()} for ${_trialPlan.formattedDurationAfterTrial}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Benefits section
                      const Text(
                        'Premium Benefits',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.premiumGold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Benefits list
                      _buildBenefitItem(
                        icon: Icons.style,
                        title: 'Exclusive Templates',
                        description: 'Access to premium templates not available in the free version',
                      ),
                      _buildBenefitItem(
                        icon: Icons.remove_red_eye,
                        title: 'No Watermarks',
                        description: 'Create posters without QuickPosters watermark',
                      ),
                      _buildBenefitItem(
                        icon: Icons.auto_awesome,
                        title: 'Advanced AI Features',
                        description: 'Enhanced AI image generation and logo creation tools',
                      ),
                      _buildBenefitItem(
                        icon: Icons.color_lens,
                        title: 'Premium Theme',
                        description: 'Elegant gold and black theme for a premium experience',
                      ),
                      _buildBenefitItem(
                        icon: Icons.cloud_upload,
                        title: 'Priority Cloud Storage',
                        description: 'Store more posters and access them from any device',
                      ),
                      _buildBenefitItem(
                        icon: Icons.support_agent,
                        title: 'Priority Support',
                        description: 'Get faster responses to your questions and issues',
                      ),

                      const SizedBox(height: 24),

                      // Mandate information
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(26), // 0.1 * 255 = 26
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'Subscription Details',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Your subscription will automatically renew at ₹${_trialPlan.priceAfterTrial?.toInt()} every ${_trialPlan.formattedDurationAfterTrial} after the trial period. You can cancel anytime before the trial ends to avoid being charged.',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 100), // Space for the button at bottom
                    ],
                  ),
                ),
              ),

              // Start Trial button at bottom
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withAlpha(0), // 0.0 * 255 = 0
                        Colors.black,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    top: false,
                    child: GradientButton(
                      text: 'Start ${_trialPlan.formattedTrialDuration} Trial for ₹${_trialPlan.price.toInt()}',
                      onPressed: _isLoading ? () {} : () => _startTrial(context),
                      isLoading: _isLoading,
                      gradient: const LinearGradient(
                        colors: [
                          AppTheme.premiumGold,
                          Color(0xFFD4AF37),
                        ],
                      ),
                      textStyle: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build a benefit item with icon, title and description
  Widget _buildBenefitItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.premiumGold.withAlpha(51), // 0.2 * 255 = 51
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppTheme.premiumGold,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Start the trial subscription
  Future<void> _startTrial(BuildContext context) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Store context in local variable to avoid using context across async gaps
      final currentContext = context;

      final subscriptionService = currentContext.read<SubscriptionService>();
      final success = await subscriptionService.purchaseSubscription(
        _trialPlan,
        currentContext,
      );

      if (success && mounted) {
        // Get a fresh context after the async gap
        final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
        await themeProvider.updatePremiumStatus(true);

        // Check mounted again after another async gap
        if (mounted) {
          // Navigate back to home
          Navigator.of(context).pop();

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Premium trial activated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start trial: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
