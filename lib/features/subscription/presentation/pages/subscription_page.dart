import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../../domain/entities/subscription_plan.dart';
import '../../domain/usecases/subscription_service.dart';

/// Page for displaying and selecting subscription plans
class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({super.key});

  @override
  State<SubscriptionPage> createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  SubscriptionPlan? _selectedPlan;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Set the recommended plan as the default selected plan
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final subscriptionService = context.read<SubscriptionService>();
      setState(() {
        _selectedPlan = subscriptionService.getRecommendedPlan();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;
    final subscriptionService = context.read<SubscriptionService>();
    final plans = subscriptionService.getAvailablePlans();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Premium Subscription',
          style: TextStyle(
            color: isPremium ? AppTheme.premiumGold : null,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: isPremium ? AppTheme.premiumBlack : null,
        iconTheme: IconThemeData(
          color: isPremium ? AppTheme.premiumGold : null,
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: isPremium
              ? const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.premiumBlack,
                    Color(0xFF1A1A1A),
                  ],
                )
              : null,
          color: isPremium ? null : Colors.white,
        ),
        child: Column(
          children: [
            // Header section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.workspace_premium,
                    size: 64,
                    color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Unlock Premium Features',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Choose a plan that works for you',
                    style: TextStyle(
                      fontSize: 16,
                      color: isPremium ? Colors.white70 : Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // Subscription plans
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: plans.length,
                itemBuilder: (context, index) {
                  final plan = plans[index];
                  final isSelected = _selectedPlan?.id == plan.id;

                  return _buildPlanCard(
                    plan: plan,
                    isSelected: isSelected,
                    isPremium: isPremium,
                    onTap: () {
                      setState(() {
                        _selectedPlan = plan;
                      });
                    },
                  );
                },
              ),
            ),

            // Purchase button
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  GradientButton(
                    text: 'Subscribe Now',
                    onPressed: _selectedPlan == null || _isLoading
                        ? () {}
                        : () => _purchaseSubscription(context),
                    isLoading: _isLoading,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Secure payment via Google Play / App Store',
                    style: TextStyle(
                      fontSize: 12,
                      color: isPremium ? Colors.white54 : Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a card for a subscription plan
  Widget _buildPlanCard({
    required SubscriptionPlan plan,
    required bool isSelected,
    required bool isPremium,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: isPremium
              ? (isSelected ? AppTheme.premiumDarkGrey : Colors.black54)
              : (isSelected ? Colors.blue.withAlpha(26) : Colors.grey[100]), // 0.1 * 255 = 26
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                : (isPremium ? Colors.grey[800]! : Colors.grey[300]!),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            // Plan content
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Plan name and price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        plan.name,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            plan.formattedPrice,
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                            ),
                          ),
                          Text(
                            'for ${plan.formattedDuration}',
                            style: TextStyle(
                              fontSize: 14,
                              color: isPremium ? Colors.white70 : Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Plan description
                  Text(
                    plan.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isPremium ? Colors.white70 : Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Trial information (if applicable)
                  if (plan.hasTrial && plan.trialDays != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: isPremium
                            ? AppTheme.premiumGold.withAlpha(51) // 0.2 * 255 = 51
                            : Colors.blue.withAlpha(26), // 0.1 * 255 = 26
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '${plan.formattedTrialDuration} trial, then ${plan.formattedPriceAfterTrial} for ${plan.formattedDurationAfterTrial}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                        ),
                      ),
                    ),
                  const SizedBox(height: 16),

                  // Features
                  ...plan.features.map((feature) => Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.check_circle,
                              size: 18,
                              color: isPremium ? AppTheme.premiumGold : Colors.green,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                feature,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: isPremium ? Colors.white : Colors.black87,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),

                  // Savings badge (for non-trial plans)
                  if (!plan.hasTrial && plan.savingsPercentage > 0)
                    Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: isPremium
                              ? AppTheme.premiumGold.withAlpha(51) // 0.2 * 255 = 51
                              : Colors.green.withAlpha(26), // 0.1 * 255 = 26
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          'Save ${plan.savingsPercentage.toInt()}%',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: isPremium ? AppTheme.premiumGold : Colors.green,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Recommended badge
            if (plan.isRecommended)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: isPremium ? AppTheme.premiumGold : Colors.blue,
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(16),
                      bottomLeft: Radius.circular(16),
                    ),
                  ),
                  child: Text(
                    'BEST VALUE',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: isPremium ? Colors.black : Colors.white,
                    ),
                  ),
                ),
              ),

            // Selected indicator
            if (isSelected)
              Positioned(
                top: 16,
                left: 16,
                child: Icon(
                  Icons.check_circle,
                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                  size: 24,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Purchase the selected subscription plan
  Future<void> _purchaseSubscription(BuildContext context) async {
    if (_selectedPlan == null) return;

    setState(() {
      _isLoading = true;
    });

    final subscriptionService = context.read<SubscriptionService>();
    final success = await subscriptionService.purchaseSubscription(
      _selectedPlan!,
      context,
    );

    if (success && mounted) {
      // Update the theme provider
      final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
      await themeProvider.updatePremiumStatus(true);

      // Check mounted again after async operation
      if (mounted) {
        // Navigate back
        Navigator.of(context).pop();
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
