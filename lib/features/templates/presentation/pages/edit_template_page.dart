import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';

import '../../../../core/providers/theme_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../user/domain/entities/user_model.dart';
import '../../../user/domain/entities/party.dart';
import '../../../user/domain/usecases/user_service.dart';
import '../../../user/domain/usecases/party_service.dart';
import '../../../banners/domain/entities/banner_item.dart';
import '../../../banners/domain/usecases/banner_service.dart';
import '../../domain/entities/template_item.dart';
import '../widgets/template_card_components/sharing_utilities.dart';
import '../widgets/template_card_components/action_buttons.dart';
import '../widgets/template_card_components/party_components.dart';
import '../widgets/template_card_components/profile_image_handler.dart';
import '../widgets/template_card_components/user_info_components.dart';

class EditTemplatePage extends StatefulWidget {
  final TemplateItem template;
  final BannerItem? initialBanner;

  const EditTemplatePage({
    super.key,
    required this.template,
    this.initialBanner,
  });

  @override
  State<EditTemplatePage> createState() => _EditTemplatePageState();
}

class _EditTemplatePageState extends State<EditTemplatePage> {
  final ScreenshotController _screenshotController = ScreenshotController();

  UserModel? _user;
  Party? _selectedParty;
  BannerItem? _selectedBanner;
  List<BannerItem> _banners = [];
  bool _isLoadingUser = true;
  bool _isLoadingBanners = false;
  bool _isLoadingMoreBanners = false;
  List<PartyLeader> _partyLeadership = [];
  final ScrollController _bannerScrollController = ScrollController();


  // Profile image positioning and scaling
  Offset _profileImageOffset = Offset.zero;
  double _profileImageScale = 1.0;

  @override
  void initState() {
    super.initState();
    _selectedBanner = widget.initialBanner;
    _loadUserData();
    _loadInitialBanners();
    _bannerScrollController.addListener(_onBannerScroll);
  }

  @override
  void dispose() {
    _bannerScrollController.removeListener(_onBannerScroll);
    _bannerScrollController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final userService = Provider.of<UserService>(context, listen: false);
      final user = await userService.getCurrentUser();

      if (user != null && user.userType == 'politician') {
        if (!mounted) return;
        final partyService = Provider.of<PartyService>(context, listen: false);
        final parties = await partyService.getAllParties();

        // Find the user's selected party based on party_name parameter
        if (user.politicalProfile?.parameterValues != null) {
          final partyName = user.politicalProfile!.parameterValues!['party_name'];
          if (partyName != null) {
            _selectedParty = parties.firstWhere(
              (party) => party.id == partyName || party.name == partyName,
              orElse: () => parties.isNotEmpty ? parties.first : parties.first,
            );

            // Load party leadership
            if (_selectedParty != null) {
              _partyLeadership = _selectedParty!.leadership;
            }
          }
        }
      }

      if (mounted) {
        setState(() {
          _user = user;
          _isLoadingUser = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingUser = false;
        });
      }
    }
  }

  Future<void> _loadInitialBanners() async {
    if (_isLoadingBanners) return;

    setState(() {
      _isLoadingBanners = true;
    });

    try {
      final bannerService = Provider.of<BannerService>(context, listen: false);
      bannerService.resetPagination(); // Reset pagination
      final banners = await bannerService.getBanners(limit: 5);

      if (mounted) {
        setState(() {
          _banners = banners;
          _isLoadingBanners = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingBanners = false;
        });
      }
    }
  }

  Future<void> _loadMoreBanners() async {
    final bannerService = Provider.of<BannerService>(context, listen: false);

    if (_isLoadingMoreBanners || !bannerService.hasMoreBanners) return;

    setState(() {
      _isLoadingMoreBanners = true;
    });

    try {
      final moreBanners = await bannerService.getBanners(limit: 5);

      if (mounted && moreBanners.isNotEmpty) {
        setState(() {
          _banners.addAll(moreBanners);
          _isLoadingMoreBanners = false;
        });
      } else if (mounted) {
        setState(() {
          _isLoadingMoreBanners = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading more banners: $e');
      if (mounted) {
        setState(() {
          _isLoadingMoreBanners = false;
        });
      }
    }
  }

  void _onBannerScroll() {
    if (_bannerScrollController.hasClients &&
        _bannerScrollController.position.pixels >=
        _bannerScrollController.position.maxScrollExtent - 100) {
      _loadMoreBanners();
    }
  }

  void _selectBanner(BannerItem? banner) {
    setState(() {
      _selectedBanner = banner;
    });
  }



  String? _getProfilePhotoUrl() {
    if (_user == null) {
      return null;
    }

    if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
      // Business user - prioritize business logo
      // First check if logoUrl is available
      if (_user!.businessProfile!.logoUrl != null && _user!.businessProfile!.logoUrl!.isNotEmpty) {
        return _user!.businessProfile!.logoUrl;
      }

      // If not, check if there's a logo in the parameter values
      if (_user!.businessProfile!.parameterValues != null) {
        // Look for any parameter that might contain a logo URL
        for (var entry in _user!.businessProfile!.parameterValues!.entries) {
          if (entry.key.toLowerCase().contains('logo') &&
              entry.value is String &&
              (entry.value as String).isNotEmpty) {
            return entry.value as String;
          }
        }
      }

      // If still no logo, use the user's photo as fallback
      if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
        return _user!.photoUrl;
      }
    } else if (_user!.userType == 'politician' && _user!.politicalProfile != null) {
      // Political user - prioritize political photo
      final paramValues = _user!.politicalProfile!.parameterValues;
      if (paramValues != null) {
        final photoUrl = paramValues['political_photo'] as String?;
        if (photoUrl != null && photoUrl.isNotEmpty) {
          return photoUrl;
        }
      }

      // If no political photo, use the user's photo as fallback
      if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
        return _user!.photoUrl;
      }
    } else {
      // Regular user - use profile photo
      if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
        return _user!.photoUrl;
      }
    }

    return null;
  }

  void _constrainProfileImageOffset() {
    final screenSize = MediaQuery.of(context).size;
    final templateWidth = screenSize.width - 32; // Account for padding
    final templateHeight = screenSize.height * 0.6;
    final imageSize = 64.0;

    // Calculate boundaries
    final leftBoundary = 0.0;
    final rightBoundary = templateWidth - imageSize;
    final topBoundary = 0.0;
    final bottomBoundary = templateHeight - imageSize;

    // If banner exists, adjust the bottom boundary
    final bannerHeight = _selectedBanner != null ? 80.0 : 0.0;
    final absoluteBottomBoundary = bottomBoundary + bannerHeight;

    // Calculate initial position (bottom-right with padding)
    final initialX = templateWidth - imageSize - 40;
    final initialY = templateHeight - imageSize - 40;

    // Get absolute position
    final absoluteX = initialX + _profileImageOffset.dx;
    final absoluteY = initialY + _profileImageOffset.dy;

    // Constrain position
    final constrainedX = absoluteX.clamp(leftBoundary, rightBoundary);
    final constrainedY = absoluteY.clamp(topBoundary, absoluteBottomBoundary);

    // Update offset
    _profileImageOffset = Offset(
      constrainedX - initialX,
      constrainedY - initialY,
    );
  }

  Widget? _buildTopBanner(bool isPremium) {
    if (_user == null) return null;

    String? displayName;

    // Only show top banner for business users
    if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
      displayName = _user!.businessProfile!.businessName;

      if ((displayName == null || displayName.isEmpty) &&
          _user!.businessProfile!.parameterValues != null) {
        final businessNameParam = _user!.businessProfile!.parameterValues!['business_name'] ??
                                 _user!.businessProfile!.parameterValues!['company_name'] ??
                                 _user!.businessProfile!.parameterValues!['organization_name'];

        if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
          displayName = businessNameParam;
        }
      }
    }

    if (displayName == null || displayName.isEmpty) return null;

    return PartyComponents.buildTopBanner(displayName.toUpperCase(), isPremium);
  }

  Widget _buildTemplatePreview(bool isPremium) {
    final screenSize = MediaQuery.of(context).size;
    final templateWidth = screenSize.width - 32;
    final templateHeight = screenSize.height * 0.6;
    final String? photoUrl = _getProfilePhotoUrl();

    return Screenshot(
      controller: _screenshotController,
      child: Container(
        width: templateWidth,
        height: templateHeight,
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // Template image
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: widget.template.imageUrl,
                  fit: BoxFit.fill,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.error),
                  ),
                ),
              ),

              // Top banner for business name
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _buildTopBanner(isPremium) ?? const SizedBox.shrink(),
              ),

              // Political party logo (top right)
              if (_user?.userType == 'politician' && _selectedParty != null)
                Positioned(
                  top: 16,
                  right: 16,
                  child: PartyComponents.buildPartyLogo(_selectedParty!.logo) ?? const SizedBox.shrink(),
                ),

              // Leadership images on the left side in a row (if user is a politician)
              if (_user?.userType == 'politician' && _partyLeadership.isNotEmpty)
                Positioned(
                  top: 16,
                  left: 16,
                  child: PartyComponents.buildLeadershipImages(_partyLeadership) ?? const SizedBox.shrink(),
                ),

              // Banner at bottom with user info overlay (always show for user info)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Stack(
                  children: [
                    // Banner image or transparent background
                    Container(
                      height: 80,
                      width: double.infinity,
                      child: _selectedBanner != null
                          ? CachedNetworkImage(
                              imageUrl: _selectedBanner!.imageUrl,
                              height: 80,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                height: 80,
                                color: Colors.grey[200],
                                child: const Center(child: CircularProgressIndicator()),
                              ),
                              errorWidget: (context, url, error) => Container(
                                height: 80,
                                color: Colors.grey[200],
                                child: const Icon(Icons.error),
                              ),
                            )
                          : Container(
                              height: 80,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.black.withAlpha(128), // Semi-transparent black background
                                border: Border(
                                  top: BorderSide(
                                    color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.white.withAlpha(100),
                                    width: 1,
                                  ),
                                ),
                              ),
                            ),
                    ),

                    // User information overlay (interactive for editing)
                    UserInfoComponents.buildUserInfoOverlay(_user, isPremium, isForSharing: false),
                  ],
                ),
              ),

              // Profile photo with draggable functionality
              if (photoUrl != null)
                Positioned(
                  left: templateWidth - 64 - 40 + _profileImageOffset.dx,
                  top: templateHeight - 64 - 40 + _profileImageOffset.dy,
                  child: ProfileImageHandler(
                    photoUrl: photoUrl,
                    isPremium: isPremium,
                    initialOffset: _profileImageOffset,
                    initialScale: _profileImageScale,
                    onScaleChanged: (scale) {
                      setState(() {
                        _profileImageScale = scale;
                      });
                    },
                    onOffsetChanged: (offset) {
                      setState(() {
                        _profileImageOffset = offset;
                        _constrainProfileImageOffset();
                      });
                    },
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBannerSelectionList(bool isPremium) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Select Banner',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
        ),

        // Horizontal banner list
        SizedBox(
          height: 100,
          child: _isLoadingBanners
              ? const Center(child: CircularProgressIndicator())
              : ListView.builder(
                  controller: _bannerScrollController,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _banners.length + 1 + (_isLoadingMoreBanners ? 1 : 0), // +1 for "No Banner", +1 for loading
                  itemBuilder: (context, index) {
                    // "No Banner" option
                    if (index == 0) {
                      return _buildBannerItem(
                        null,
                        'No Banner',
                        isPremium,
                        isSelected: _selectedBanner == null,
                      );
                    }

                    // Regular banners
                    final bannerIndex = index - 1;
                    if (bannerIndex >= 0 && bannerIndex < _banners.length) {
                      final banner = _banners[bannerIndex];
                      return _buildBannerItem(
                        banner,
                        banner.title ?? 'Banner',
                        isPremium,
                        isSelected: _selectedBanner?.imageUrl == banner.imageUrl,
                      );
                    }

                    // Loading indicator
                    if (_isLoadingMoreBanners && bannerIndex == _banners.length) {
                      return Container(
                        width: 80,
                        margin: const EdgeInsets.only(right: 8),
                        child: const Center(child: CircularProgressIndicator()),
                      );
                    }

                    return const SizedBox.shrink();
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildBannerItem(BannerItem? banner, String title, bool isPremium, {required bool isSelected}) {
    return GestureDetector(
      onTap: () => _selectBanner(banner),
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                : Colors.grey.withAlpha(100),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Column(
          children: [
            // Banner image or placeholder
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                child: banner != null
                    ? CachedNetworkImage(
                        imageUrl: banner.imageUrl,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[200],
                          child: const Center(
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[200],
                          child: const Icon(Icons.error, size: 20),
                        ),
                      )
                    : Container(
                        color: Colors.grey[200],
                        child: const Icon(Icons.clear, size: 30),
                      ),
              ),
            ),

            // Title
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
              decoration: BoxDecoration(
                color: isSelected
                    ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                    : Colors.grey[100],
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
              ),
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.white : Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Edit Template',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _profileImageOffset = Offset.zero;
                _profileImageScale = 1.0;
              });
            },
          ),
        ],
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _isLoadingUser
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // Template preview
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildTemplatePreview(isPremium),

                          // Banner selection horizontal list
                          _buildBannerSelectionList(isPremium),

                          const SizedBox(height: 16),


                        ],
                      ),
                    ),
                  ),

                  // Action buttons
                  ActionButtons.buildSharingButtonsRow(
                    context: context,
                    isPremium: isPremium,
                    onDownload: () => SharingUtilities.downloadTemplate(context, _screenshotController),
                    onShareWhatsApp: () => SharingUtilities.shareToWhatsApp(context, _screenshotController),
                    onShare: () => SharingUtilities.shareTemplate(context, _screenshotController),
                  ),
                ],
              ),
      ),
    );
  }
}
