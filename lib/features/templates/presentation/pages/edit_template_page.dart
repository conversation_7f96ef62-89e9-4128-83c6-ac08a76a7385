import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';

import '../../../../core/providers/theme_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../user/domain/entities/user_model.dart';
import '../../../user/domain/entities/party.dart';
import '../../../user/domain/usecases/user_service.dart';
import '../../../user/domain/usecases/party_service.dart';
import '../../../banners/domain/entities/banner_item.dart';
import '../../../banners/domain/usecases/banner_service.dart';
import '../../domain/entities/template_item.dart';
import '../widgets/template_card_components/sharing_utilities.dart';
import '../widgets/template_card_components/action_buttons.dart';
import '../widgets/template_card_components/party_components.dart';
import '../widgets/template_card_components/profile_image_handler.dart';

class EditTemplatePage extends StatefulWidget {
  final TemplateItem template;
  final BannerItem? initialBanner;

  const EditTemplatePage({
    super.key,
    required this.template,
    this.initialBanner,
  });

  @override
  State<EditTemplatePage> createState() => _EditTemplatePageState();
}

class _EditTemplatePageState extends State<EditTemplatePage> {
  final ScreenshotController _screenshotController = ScreenshotController();

  UserModel? _user;
  Party? _selectedParty;
  BannerItem? _selectedBanner;
  List<BannerItem> _banners = [];
  bool _isLoadingUser = true;
  bool _isLoadingBanners = true;
  List<PartyLeader> _partyLeadership = [];


  // Profile image positioning and scaling
  Offset _profileImageOffset = Offset.zero;
  double _profileImageScale = 1.0;

  // Text editing state
  String? _customBusinessName;
  String? _customUserName;

  @override
  void initState() {
    super.initState();
    _selectedBanner = widget.initialBanner;
    _loadUserData();
    _loadBanners();
  }

  Future<void> _loadUserData() async {
    try {
      final userService = Provider.of<UserService>(context, listen: false);
      final user = await userService.getCurrentUser();

      if (user != null && user.userType == 'politician') {
        if (!mounted) return;
        final partyService = Provider.of<PartyService>(context, listen: false);
        final parties = await partyService.getAllParties();

        // Find the user's selected party based on party_name parameter
        if (user.politicalProfile?.parameterValues != null) {
          final partyName = user.politicalProfile!.parameterValues!['party_name'];
          if (partyName != null) {
            _selectedParty = parties.firstWhere(
              (party) => party.id == partyName || party.name == partyName,
              orElse: () => parties.isNotEmpty ? parties.first : parties.first,
            );

            // Load party leadership
            if (_selectedParty != null) {
              _partyLeadership = _selectedParty!.leadership;
            }
          }
        }
      }

      if (mounted) {
        setState(() {
          _user = user;
          _isLoadingUser = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingUser = false;
        });
      }
    }
  }

  Future<void> _loadBanners() async {
    try {
      final bannerService = Provider.of<BannerService>(context, listen: false);
      final banners = await bannerService.getAllBanners();

      if (mounted) {
        setState(() {
          _banners = banners;
          _isLoadingBanners = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingBanners = false;
        });
      }
    }
  }

  void _showBannerSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Select Banner'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: _isLoadingBanners
                ? const Center(child: CircularProgressIndicator())
                : ListView.builder(
                    itemCount: _banners.length + 1, // +1 for "No Banner" option
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return ListTile(
                          leading: const Icon(Icons.clear),
                          title: const Text('No Banner'),
                          selected: _selectedBanner == null,
                          onTap: () {
                            setState(() {
                              _selectedBanner = null;
                            });
                            Navigator.pop(context);
                          },
                        );
                      }

                      final banner = _banners[index - 1];
                      return ListTile(
                        leading: CachedNetworkImage(
                          imageUrl: banner.imageUrl,
                          width: 50,
                          height: 30,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => const SizedBox(
                            width: 50,
                            height: 30,
                            child: Center(child: CircularProgressIndicator()),
                          ),
                          errorWidget: (context, url, error) => const Icon(Icons.error),
                        ),
                        title: Text(banner.title ?? 'Banner'),
                        selected: _selectedBanner?.imageUrl == banner.imageUrl,
                        onTap: () {
                          setState(() {
                            _selectedBanner = banner;
                          });
                          Navigator.pop(context);
                        },
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  void _showTextEditDialog(String textType, String? currentText) {
    final TextEditingController controller = TextEditingController(text: currentText ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(textType == 'business_name' ? 'Edit Business Name' : 'Edit Name'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: textType == 'business_name' ? 'Business Name' : 'Name',
            border: const OutlineInputBorder(),
          ),
          textCapitalization: TextCapitalization.words,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                if (textType == 'business_name') {
                  _customBusinessName = controller.text.trim().isEmpty ? null : controller.text.trim();
                } else {
                  _customUserName = controller.text.trim().isEmpty ? null : controller.text.trim();
                }
              });
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  String? _getProfilePhotoUrl() {
    if (_user == null) {
      return null;
    }

    if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
      // Business user - prioritize business logo
      // First check if logoUrl is available
      if (_user!.businessProfile!.logoUrl != null && _user!.businessProfile!.logoUrl!.isNotEmpty) {
        return _user!.businessProfile!.logoUrl;
      }

      // If not, check if there's a logo in the parameter values
      if (_user!.businessProfile!.parameterValues != null) {
        // Look for any parameter that might contain a logo URL
        for (var entry in _user!.businessProfile!.parameterValues!.entries) {
          if (entry.key.toLowerCase().contains('logo') &&
              entry.value is String &&
              (entry.value as String).isNotEmpty) {
            return entry.value as String;
          }
        }
      }

      // If still no logo, use the user's photo as fallback
      if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
        return _user!.photoUrl;
      }
    } else if (_user!.userType == 'politician' && _user!.politicalProfile != null) {
      // Political user - prioritize political photo
      final paramValues = _user!.politicalProfile!.parameterValues;
      if (paramValues != null) {
        final photoUrl = paramValues['political_photo'] as String?;
        if (photoUrl != null && photoUrl.isNotEmpty) {
          return photoUrl;
        }
      }

      // If no political photo, use the user's photo as fallback
      if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
        return _user!.photoUrl;
      }
    } else {
      // Regular user - use profile photo
      if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
        return _user!.photoUrl;
      }
    }

    return null;
  }

  void _constrainProfileImageOffset() {
    final screenSize = MediaQuery.of(context).size;
    final templateWidth = screenSize.width - 32; // Account for padding
    final templateHeight = screenSize.height * 0.6;
    final imageSize = 64.0;

    // Calculate boundaries
    final leftBoundary = 0.0;
    final rightBoundary = templateWidth - imageSize;
    final topBoundary = 0.0;
    final bottomBoundary = templateHeight - imageSize;

    // If banner exists, adjust the bottom boundary
    final bannerHeight = _selectedBanner != null ? 80.0 : 0.0;
    final absoluteBottomBoundary = bottomBoundary + bannerHeight;

    // Calculate initial position (bottom-right with padding)
    final initialX = templateWidth - imageSize - 40;
    final initialY = templateHeight - imageSize - 40;

    // Get absolute position
    final absoluteX = initialX + _profileImageOffset.dx;
    final absoluteY = initialY + _profileImageOffset.dy;

    // Constrain position
    final constrainedX = absoluteX.clamp(leftBoundary, rightBoundary);
    final constrainedY = absoluteY.clamp(topBoundary, absoluteBottomBoundary);

    // Update offset
    _profileImageOffset = Offset(
      constrainedX - initialX,
      constrainedY - initialY,
    );
  }

  Widget? _buildTopBanner(bool isPremium) {
    if (_user == null) return null;

    String? displayName;

    // Only show top banner for business users
    if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
      // Use custom business name if set, otherwise use original
      displayName = _customBusinessName ?? _user!.businessProfile!.businessName;

      if ((displayName == null || displayName.isEmpty) &&
          _user!.businessProfile!.parameterValues != null) {
        final businessNameParam = _user!.businessProfile!.parameterValues!['business_name'] ??
                                 _user!.businessProfile!.parameterValues!['company_name'] ??
                                 _user!.businessProfile!.parameterValues!['organization_name'];

        if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
          displayName = businessNameParam;
        }
      }
    }

    if (displayName == null || displayName.isEmpty) return null;

    final bannerWidget = PartyComponents.buildTopBanner(displayName.toUpperCase(), isPremium);
    if (bannerWidget == null) return null;

    return GestureDetector(
      onTap: () => _showTextEditDialog('business_name', displayName),
      child: Stack(
        children: [
          bannerWidget,
          // Edit icon overlay
          Positioned(
            right: 8,
            top: 8,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(100),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.edit,
                size: 16,
                color: isPremium ? AppTheme.premiumGold : Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatePreview(bool isPremium) {
    final screenSize = MediaQuery.of(context).size;
    final templateWidth = screenSize.width - 32;
    final templateHeight = screenSize.height * 0.6;
    final String? photoUrl = _getProfilePhotoUrl();

    return Screenshot(
      controller: _screenshotController,
      child: Container(
        width: templateWidth,
        height: templateHeight,
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // Template image
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: widget.template.imageUrl,
                  fit: BoxFit.fill,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.error),
                  ),
                ),
              ),

              // Top banner for business name
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _buildTopBanner(isPremium) ?? const SizedBox.shrink(),
              ),

              // Political party logo (top right)
              if (_user?.userType == 'politician' && _selectedParty != null)
                Positioned(
                  top: 16,
                  right: 16,
                  child: PartyComponents.buildPartyLogo(_selectedParty!.logo) ?? const SizedBox.shrink(),
                ),

              // Leadership images on the left side in a row (if user is a politician)
              if (_user?.userType == 'politician' && _partyLeadership.isNotEmpty)
                Positioned(
                  top: 16,
                  left: 16,
                  child: PartyComponents.buildLeadershipImages(_partyLeadership) ?? const SizedBox.shrink(),
                ),

              // Banner at bottom
              if (_selectedBanner != null)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: _showBannerSelectionDialog,
                    child: CachedNetworkImage(
                      imageUrl: _selectedBanner!.imageUrl,
                      height: 80,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        height: 80,
                        color: Colors.grey[200],
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                      errorWidget: (context, url, error) => Container(
                        height: 80,
                        color: Colors.grey[200],
                        child: const Icon(Icons.error),
                      ),
                    ),
                  ),
                ),

              // Profile photo with draggable functionality
              if (photoUrl != null)
                Positioned(
                  left: templateWidth - 64 - 40 + _profileImageOffset.dx,
                  top: templateHeight - 64 - 40 + _profileImageOffset.dy,
                  child: ProfileImageHandler(
                    photoUrl: photoUrl,
                    isPremium: isPremium,
                    initialOffset: _profileImageOffset,
                    initialScale: _profileImageScale,
                    onScaleChanged: (scale) {
                      setState(() {
                        _profileImageScale = scale;
                      });
                    },
                    onOffsetChanged: (offset) {
                      setState(() {
                        _profileImageOffset = offset;
                        _constrainProfileImageOffset();
                      });
                    },
                  ),
                ),

              // User name display (for regular users and politicians)
              if (_user != null && (_user!.userType == 'regular' || _user!.userType == 'politician'))
                Positioned(
                  left: templateWidth - 200 + _profileImageOffset.dx,
                  top: templateHeight - 30 + _profileImageOffset.dy,
                  child: GestureDetector(
                    onTap: () => _showTextEditDialog('user_name', _customUserName ?? _user!.name),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(150),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _customUserName ?? _user!.name,
                            style: TextStyle(
                              color: isPremium ? AppTheme.premiumGold : Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.edit,
                            size: 12,
                            color: isPremium ? AppTheme.premiumGold : Colors.white70,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Edit Template',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _profileImageOffset = Offset.zero;
                _profileImageScale = 1.0;
              });
            },
          ),
        ],
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _isLoadingUser
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // Template preview
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildTemplatePreview(isPremium),

                          // Banner selection button
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: ElevatedButton.icon(
                              onPressed: _showBannerSelectionDialog,
                              icon: const Icon(Icons.flag),
                              label: Text(_selectedBanner != null
                                  ? 'Change Banner'
                                  : 'Add Banner'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isPremium
                                    ? AppTheme.premiumGold
                                    : AppTheme.primaryBlue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Text editing controls
                          if (_user != null) ...[
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              child: Column(
                                children: [
                                  // Business name editing (for business users)
                                  if (_user!.userType == 'businessman')
                                    ElevatedButton.icon(
                                      onPressed: () => _showTextEditDialog('business_name', _customBusinessName ?? _user!.businessProfile?.businessName),
                                      icon: const Icon(Icons.business),
                                      label: const Text('Edit Business Name'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: isPremium
                                            ? AppTheme.premiumGold.withAlpha(200)
                                            : AppTheme.primaryBlue.withAlpha(200),
                                        foregroundColor: Colors.white,
                                      ),
                                    ),

                                  const SizedBox(height: 8),

                                  // User name editing (for all users)
                                  ElevatedButton.icon(
                                    onPressed: () => _showTextEditDialog('user_name', _customUserName ?? _user!.name),
                                    icon: const Icon(Icons.person),
                                    label: const Text('Edit Your Name'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: isPremium
                                          ? AppTheme.premiumGold.withAlpha(200)
                                          : AppTheme.primaryBlue.withAlpha(200),
                                      foregroundColor: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),
                          ],
                        ],
                      ),
                    ),
                  ),

                  // Action buttons
                  ActionButtons.buildSharingButtonsRow(
                    context: context,
                    isPremium: isPremium,
                    onDownload: () => SharingUtilities.downloadTemplate(context, _screenshotController),
                    onShareWhatsApp: () => SharingUtilities.shareToWhatsApp(context, _screenshotController),
                    onShare: () => SharingUtilities.shareTemplate(context, _screenshotController),
                  ),
                ],
              ),
      ),
    );
  }
}
