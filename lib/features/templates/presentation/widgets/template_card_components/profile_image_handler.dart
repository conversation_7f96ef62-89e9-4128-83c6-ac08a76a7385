import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../../core/theme/app_theme.dart';

/// A widget that handles the profile image display, dragging and scaling
class ProfileImageHandler extends StatefulWidget {
  /// The URL of the profile image
  final String photoUrl;
  
  /// Whether the user has premium status
  final bool isPremium;
  
  /// Initial offset for the profile image
  final Offset initialOffset;
  
  /// Initial scale for the profile image
  final double initialScale;
  
  /// Callback when the scale changes
  final Function(double) onScaleChanged;
  
  /// Callback when the offset changes
  final Function(Offset) onOffsetChanged;

  const ProfileImageHandler({
    Key? key,
    required this.photoUrl,
    required this.isPremium,
    this.initialOffset = Offset.zero,
    this.initialScale = 1.0,
    required this.onScaleChanged,
    required this.onOffsetChanged,
  }) : super(key: key);

  @override
  State<ProfileImageHandler> createState() => _ProfileImageHandlerState();
}

class _ProfileImageHandlerState extends State<ProfileImageHandler> {
  late Offset _profileImageOffset;
  late double _profileImageScale;
  bool _isDraggingProfileImage = false;
  double? _initialScale;

  @override
  void initState() {
    super.initState();
    _profileImageOffset = widget.initialOffset;
    _profileImageScale = widget.initialScale;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Use GestureDetector with direct scale handling for immediate scaling and dragging
      // This keeps the image visible during the entire operation
      // Set behavior to translucent to ensure this GestureDetector wins the gesture arena
      behavior: HitTestBehavior.translucent,

      // Handle tap to show size selection dialog
      onTap: () {
        _showSizeSelectionDialog(context);
      },

      // Handle scale gestures (pinch to zoom)
      onScaleStart: (ScaleStartDetails details) {
        setState(() {
          _isDraggingProfileImage = true;
          _initialScale = _profileImageScale;
        });
      },
      onScaleUpdate: (ScaleUpdateDetails details) {
        setState(() {
          // Handle position change (dragging)
          if (details.pointerCount == 1) {
            // Single finger drag - update position
            _profileImageOffset += details.focalPointDelta;
            widget.onOffsetChanged(_profileImageOffset);
          }

          // Handle scale change (pinch)
          if (details.scale != 1.0 && _initialScale != null) {
            // Calculate new scale with some constraints
            _profileImageScale = (_initialScale! * details.scale).clamp(0.5, 2.0);
            widget.onScaleChanged(_profileImageScale);
          }
        });
      },
      onScaleEnd: (ScaleEndDetails details) {
        setState(() {
          _isDraggingProfileImage = false;
          _initialScale = null;
        });
      },
      child: Stack(
        children: [
          // Profile image container with scaling
          Transform.scale(
            scale: _profileImageScale,
            child: Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(
                  color: _isDraggingProfileImage
                      ? Colors.blue
                      : (widget.isPremium ? AppTheme.premiumGold : Colors.white),
                  width: _isDraggingProfileImage ? 3 : 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(_isDraggingProfileImage ? 100 : 50),
                    blurRadius: _isDraggingProfileImage ? 8 : 4,
                    spreadRadius: _isDraggingProfileImage ? 1 : 0,
                  ),
                ],
              ),
              child: ClipOval(
                child: CachedNetworkImage(
                  imageUrl: widget.photoUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  errorWidget: (context, url, error) => const Icon(
                    Icons.person,
                    size: 32,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          ),

          // Drag indicator icon (always shown to indicate direct draggability)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: _isDraggingProfileImage
                    ? Colors.blue.withAlpha(220)
                    : Colors.blue.withAlpha(180),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.zoom_out_map,
                color: Colors.white,
                size: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show a dialog to select the profile image size
  void _showSizeSelectionDialog(BuildContext context) {
    // Only show the dialog if we're not currently dragging
    if (_isDraggingProfileImage) return;

    // Store the initial scale in case user cancels
    final initialScale = _profileImageScale;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Adjust Profile Image Size'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Select a size for your profile image:'),
                  const SizedBox(height: 20),
                  Slider(
                    value: _profileImageScale,
                    min: 0.5,
                    max: 2.0,
                    divisions: 15,
                    label: '${(_profileImageScale * 100).round()}%',
                    onChanged: (double value) {
                      // Update both the dialog state and the parent widget state
                      setDialogState(() {
                        setState(() {
                          _profileImageScale = value;
                          widget.onScaleChanged(_profileImageScale);
                        });
                      });
                    },
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: const [
                      Text('Small', style: TextStyle(fontSize: 12)),
                      Text('Normal', style: TextStyle(fontSize: 12)),
                      Text('Large', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // Restore the original scale if user cancels
                    setState(() {
                      _profileImageScale = initialScale;
                      widget.onScaleChanged(_profileImageScale);
                    });
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Keep the current scale and close the dialog
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('Done'),
                ),
              ],
            );
          }
        );
      },
    );
  }
  
  // Getter for the current dragging state
  bool get isDragging => _isDraggingProfileImage;
}
