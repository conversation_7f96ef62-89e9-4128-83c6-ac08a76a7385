import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

import '../../../../../core/theme/app_theme.dart';
import '../../../../user/domain/entities/user_model.dart';

/// A stateful widget that displays business user information with customization options
class BusinessUserInfoOverlay extends StatefulWidget {
  final UserModel user;
  final bool isPremium;
  final bool isForSharing;

  const BusinessUserInfoOverlay({
    Key? key,
    required this.user,
    required this.isPremium,
    this.isForSharing = false, // Default to false for normal display
  }) : super(key: key);

  @override
  State<BusinessUserInfoOverlay> createState() => _BusinessUserInfoOverlayState();
}

class _BusinessUserInfoOverlayState extends State<BusinessUserInfoOverlay> {
  // Display options
  String _nameDisplayOption = 'name'; // Options: 'name', 'business_name'
  String _contactDisplayOption = 'business_phone'; // Options: 'business_phone', 'email', 'address'

  // Text styling options for name
  Color _nameTextColor = Colors.white; // Default name text color
  String _nameTextCase = 'normal'; // Options: 'normal', 'uppercase', 'lowercase', 'capitalize'

  // Text styling options for contact info
  Color _contactTextColor = Colors.white; // Default contact text color
  String _contactTextCase = 'normal'; // Options: 'normal', 'uppercase', 'lowercase', 'capitalize'

  @override
  void initState() {
    super.initState();

    // Set default text colors based on premium status
    final defaultColor = widget.isPremium ? AppTheme.premiumGold : Colors.white;
    _nameTextColor = defaultColor;
    _contactTextColor = defaultColor;

    // Debug: Print user and business profile info
    debugPrint('BusinessUserInfoOverlay: initState - User = ${widget.user.name}');
    debugPrint('BusinessUserInfoOverlay: initState - Business profile = ${widget.user.businessProfile}');
    if (widget.user.businessProfile?.parameterValues != null) {
      debugPrint('BusinessUserInfoOverlay: initState - Parameter values:');
      widget.user.businessProfile!.parameterValues!.forEach((key, value) {
        debugPrint('BusinessUserInfoOverlay: initState - Parameter $key = $value');
      });
    }

    _initializeDisplayOptions();
  }

  void _initializeDisplayOptions() {
    // Set default display options based on available data
    if (widget.user.businessProfile != null) {
      debugPrint('BusinessUserInfoOverlay: Initializing display options for business profile');

      // Debug: Print all business profile fields
      debugPrint('BusinessUserInfoOverlay: businessName = ${widget.user.businessProfile!.businessName}');

      // Debug: Print all parameter values if available
      if (widget.user.businessProfile!.parameterValues != null) {
        debugPrint('BusinessUserInfoOverlay: Parameter values:');
        widget.user.businessProfile!.parameterValues!.forEach((key, value) {
          debugPrint('BusinessUserInfoOverlay: Parameter $key = $value');
        });
      } else {
        debugPrint('BusinessUserInfoOverlay: No parameter values available');
      }

      // Always use user's name as the default display option
      _nameDisplayOption = 'name';

      // Always use business phone as the default contact option
      _contactDisplayOption = 'business_phone';

      // Only fall back to other contact options if business phone is not available
      if ((widget.user.businessProfile!.mobileNumber == null ||
           widget.user.businessProfile!.mobileNumber!.isEmpty) &&
          widget.user.businessProfile!.parameterValues != null) {

        // Check if phone exists in parameter values
        final phoneParam = widget.user.businessProfile!.parameterValues!['mobile_number'] ??
                         widget.user.businessProfile!.parameterValues!['phone'] ??
                         widget.user.businessProfile!.parameterValues!['contact'] ??
                         widget.user.businessProfile!.parameterValues!['business_phone'];

        if (phoneParam == null || (phoneParam is String && phoneParam.isEmpty)) {
          // If no phone number is available, try email or address
          if (widget.user.email != null && widget.user.email!.isNotEmpty) {
            _contactDisplayOption = 'email';
          } else if (widget.user.businessProfile!.address != null &&
                    widget.user.businessProfile!.address!.isNotEmpty) {
            _contactDisplayOption = 'address';
          }
        }
      }
    }
  }

  // Check if business name is available from any source
  bool _hasBusinessName() {
    // Check direct businessName field
    if (widget.user.businessProfile?.businessName != null &&
        widget.user.businessProfile!.businessName!.isNotEmpty) {
      return true;
    }

    // Check parameter values
    if (widget.user.businessProfile?.parameterValues != null) {
      final businessNameParam = widget.user.businessProfile!.parameterValues!['business_name'] ??
                             widget.user.businessProfile!.parameterValues!['company_name'] ??
                             widget.user.businessProfile!.parameterValues!['organization_name'];

      if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
        return true;
      }
    }

    return false;
  }

  // Get business name for display in the bottom sheet
  String _getBusinessNameForDisplay() {
    // First check direct businessName field
    if (widget.user.businessProfile?.businessName != null &&
        widget.user.businessProfile!.businessName!.isNotEmpty) {
      return widget.user.businessProfile!.businessName!;
    }

    // Then check parameter values
    if (widget.user.businessProfile?.parameterValues != null) {
      final businessNameParam = widget.user.businessProfile!.parameterValues!['business_name'] ??
                             widget.user.businessProfile!.parameterValues!['company_name'] ??
                             widget.user.businessProfile!.parameterValues!['organization_name'];

      if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
        return businessNameParam;
      }
    }

    // Fallback (should not happen due to _hasBusinessName check)
    return "Business Name";
  }

  // Get the display name based on selected option
  String _getDisplayName() {
    String displayName;

    if (_nameDisplayOption == 'business_name') {
      // First check direct businessName field
      if (widget.user.businessProfile?.businessName != null &&
          widget.user.businessProfile!.businessName!.isNotEmpty) {
        displayName = widget.user.businessProfile!.businessName!;
      }
      // Then check parameter values
      else if (widget.user.businessProfile?.parameterValues != null) {
        final businessNameParam = widget.user.businessProfile!.parameterValues!['business_name'] ??
                               widget.user.businessProfile!.parameterValues!['company_name'] ??
                               widget.user.businessProfile!.parameterValues!['organization_name'];

        if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
          displayName = businessNameParam;
        } else {
          // Default to user's name if no business name found
          displayName = widget.user.name;
        }
      } else {
        // Default to user's name if no business profile or parameters
        displayName = widget.user.name;
      }
    } else {
      // Default to user's name
      displayName = widget.user.name;
    }

    // Apply text case transformation
    return _applyNameTextCase(displayName);
  }

  // Apply text case transformation to name
  String _applyNameTextCase(String text) {
    switch (_nameTextCase) {
      case 'uppercase':
        return text.toUpperCase();
      case 'lowercase':
        return text.toLowerCase();
      case 'capitalize':
        return _capitalizeFirstLetter(text);
      default:
        return text; // 'normal' case
    }
  }

  // Apply text case transformation to contact info
  String _applyContactTextCase(String text) {
    switch (_contactTextCase) {
      case 'uppercase':
        return text.toUpperCase();
      case 'lowercase':
        return text.toLowerCase();
      case 'capitalize':
        return _capitalizeFirstLetter(text);
      default:
        return text; // 'normal' case
    }
  }

  // Capitalize the first letter of each word
  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;

    return text.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  // Get description for the name text case
  String _getNameTextCaseDescription() {
    switch (_nameTextCase) {
      case 'uppercase':
        return 'UPPERCASE';
      case 'lowercase':
        return 'lowercase';
      case 'capitalize':
        return 'Capitalize Each Word';
      default:
        return 'Normal';
    }
  }

  // Get description for the contact text case
  String _getContactTextCaseDescription() {
    switch (_contactTextCase) {
      case 'uppercase':
        return 'UPPERCASE';
      case 'lowercase':
        return 'lowercase';
      case 'capitalize':
        return 'Capitalize Each Word';
      default:
        return 'Normal';
    }
  }

  // Get the contact info based on selected option
  String? _getContactInfo() {
    String? contactInfo;

    switch (_contactDisplayOption) {
      case 'business_phone':
        contactInfo = _getBusinessPhone();
        break;

      case 'personal_phone':
        contactInfo = widget.user.phoneNumber;
        break;

      case 'email':
        contactInfo = widget.user.email;
        break;

      case 'address':
        contactInfo = _getBusinessAddress();
        break;

      default:
        contactInfo = _getBusinessPhone();
    }

    // Apply text case transformation if contact info is not null
    return contactInfo != null ? _applyContactTextCase(contactInfo) : null;
  }

  // Helper method to get business phone from any source
  String? _getBusinessPhone() {
    // First check direct field
    if (widget.user.businessProfile?.mobileNumber != null &&
        widget.user.businessProfile!.mobileNumber!.isNotEmpty) {
      return widget.user.businessProfile!.mobileNumber;
    }

    // Then check parameter values
    if (widget.user.businessProfile?.parameterValues != null) {
      final phoneParam = widget.user.businessProfile!.parameterValues!['mobile_number'] ??
                       widget.user.businessProfile!.parameterValues!['phone'] ??
                       widget.user.businessProfile!.parameterValues!['contact'] ??
                       widget.user.businessProfile!.parameterValues!['business_phone'];

      if (phoneParam != null && phoneParam is String && phoneParam.isNotEmpty) {
        return phoneParam;
      }
    }

    return widget.user.businessProfile?.mobileNumber;
  }

  // Helper method to get business address from any source
  String? _getBusinessAddress() {
    // First check direct field
    if (widget.user.businessProfile?.address != null &&
        widget.user.businessProfile!.address!.isNotEmpty) {
      return widget.user.businessProfile!.address;
    }

    // Then check parameter values
    if (widget.user.businessProfile?.parameterValues != null) {
      final addressParam = widget.user.businessProfile!.parameterValues!['address'] ??
                         widget.user.businessProfile!.parameterValues!['business_address'] ??
                         widget.user.businessProfile!.parameterValues!['location'];

      if (addressParam != null && addressParam is String && addressParam.isNotEmpty) {
        return addressParam;
      }
    }

    return widget.user.businessProfile?.address;
  }

  // Get icon for the contact info
  IconData _getContactIcon() {
    switch (_contactDisplayOption) {
      case 'business_phone':
        return Icons.business;
      case 'personal_phone':
        return Icons.phone;
      case 'email':
        return Icons.email;
      case 'address':
        return Icons.location_on;
      default:
        return Icons.phone;
    }
  }

  // Show bottom sheet for name options
  void _showNameOptionsBottomSheet() {
    showModalBottomSheet(
      isScrollControlled: true, // Allow the bottom sheet to be larger
      context: context,
      builder: (context) {
        return SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Choose what to display',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (BuildContext context, StateSetter setModalState) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ListTile(
                          title: const Text('Your Name'),
                          subtitle: Text(widget.user.name),
                          leading: const Icon(Icons.person),
                          selected: _nameDisplayOption == 'name',
                          onTap: () {
                            setState(() {
                              _nameDisplayOption = 'name';
                            });
                            // Also update the modal state to refresh the UI
                            setModalState(() {});
                          },
                        ),
                        // Business name option - show if available from any source
                        if (_hasBusinessName())
                          ListTile(
                            title: const Text('Business Name'),
                            subtitle: Text(_getBusinessNameForDisplay()),
                            leading: const Icon(Icons.business),
                            selected: _nameDisplayOption == 'business_name',
                            onTap: () {
                              setState(() {
                                _nameDisplayOption = 'business_name';
                              });
                              // Also update the modal state to refresh the UI
                              setModalState(() {});
                            },
                          ),
                      ],
                    );
                  },
                ),

                const Divider(),

                // Name styling options
                const Padding(
                  padding: EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
                  child: Text(
                    'Name Styling',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Name text color option
                ListTile(
                  title: const Text('Name Color'),
                  leading: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: _nameTextColor,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey),
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showNameColorPicker();
                  },
                ),

                // Name text case option
                ListTile(
                  title: const Text('Name Text Case'),
                  subtitle: Text(_getNameTextCaseDescription()),
                  leading: const Icon(Icons.text_format),
                  onTap: () {
                    Navigator.pop(context);
                    _showNameTextCaseOptionsBottomSheet();
                  },
                ),

                // Done button
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        'Done',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Show color picker dialog for name
  void _showNameColorPicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pick a color for name'),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: _nameTextColor,
              onColorChanged: (Color color) {
                setState(() {
                  _nameTextColor = color;
                });
              },
              pickerAreaHeightPercent: 0.8,
              displayThumbColor: true,
              labelTypes: const [ColorLabelType.rgb, ColorLabelType.hsv, ColorLabelType.hex],
              paletteType: PaletteType.hsv,
              pickerAreaBorderRadius: const BorderRadius.all(Radius.circular(10)),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Done'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // Show color picker dialog for contact info
  void _showContactColorPicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pick a color for contact info'),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: _contactTextColor,
              onColorChanged: (Color color) {
                setState(() {
                  _contactTextColor = color;
                });
              },
              pickerAreaHeightPercent: 0.8,
              displayThumbColor: true,
              labelTypes: const [ColorLabelType.rgb, ColorLabelType.hsv, ColorLabelType.hex],
              paletteType: PaletteType.hsv,
              pickerAreaBorderRadius: const BorderRadius.all(Radius.circular(10)),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Done'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // Show text case options bottom sheet for name
  void _showNameTextCaseOptionsBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Choose text case for name',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                title: const Text('Normal'),
                subtitle: const Text('Text as entered'),
                leading: const Icon(Icons.text_format),
                selected: _nameTextCase == 'normal',
                onTap: () {
                  setState(() {
                    _nameTextCase = 'normal';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('UPPERCASE'),
                subtitle: const Text('ALL CAPITAL LETTERS'),
                leading: const Icon(Icons.text_fields),
                selected: _nameTextCase == 'uppercase',
                onTap: () {
                  setState(() {
                    _nameTextCase = 'uppercase';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('lowercase'),
                subtitle: const Text('all small letters'),
                leading: const Icon(Icons.text_fields),
                selected: _nameTextCase == 'lowercase',
                onTap: () {
                  setState(() {
                    _nameTextCase = 'lowercase';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('Capitalize Each Word'),
                subtitle: const Text('First Letter Uppercase'),
                leading: const Icon(Icons.text_fields),
                selected: _nameTextCase == 'capitalize',
                onTap: () {
                  setState(() {
                    _nameTextCase = 'capitalize';
                  });
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // Show text case options bottom sheet for contact info
  void _showContactTextCaseOptionsBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Choose text case for contact info',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                title: const Text('Normal'),
                subtitle: const Text('Text as entered'),
                leading: const Icon(Icons.text_format),
                selected: _contactTextCase == 'normal',
                onTap: () {
                  setState(() {
                    _contactTextCase = 'normal';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('UPPERCASE'),
                subtitle: const Text('ALL CAPITAL LETTERS'),
                leading: const Icon(Icons.text_fields),
                selected: _contactTextCase == 'uppercase',
                onTap: () {
                  setState(() {
                    _contactTextCase = 'uppercase';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('lowercase'),
                subtitle: const Text('all small letters'),
                leading: const Icon(Icons.text_fields),
                selected: _contactTextCase == 'lowercase',
                onTap: () {
                  setState(() {
                    _contactTextCase = 'lowercase';
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text('Capitalize Each Word'),
                subtitle: const Text('First Letter Uppercase'),
                leading: const Icon(Icons.text_fields),
                selected: _contactTextCase == 'capitalize',
                onTap: () {
                  setState(() {
                    _contactTextCase = 'capitalize';
                  });
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // Show bottom sheet for contact options
  void _showContactOptionsBottomSheet() {
    // Debug: Print available contact info
    debugPrint('BusinessUserInfoOverlay: Showing contact options');
    debugPrint('BusinessUserInfoOverlay: Business phone = ${widget.user.businessProfile?.mobileNumber}');
    debugPrint('BusinessUserInfoOverlay: Email = ${widget.user.email}');
    debugPrint('BusinessUserInfoOverlay: Address = ${widget.user.businessProfile?.address}');

    showModalBottomSheet(
      isScrollControlled: true, // Allow the bottom sheet to be larger
      context: context,
      builder: (context) {
        return SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Choose what to display',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                StatefulBuilder(
                  builder: (BuildContext context, StateSetter setModalState) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Always show Business Phone option
                        ListTile(
                          title: const Text('Business Phone'),
                          subtitle: Text(_getBusinessPhone() ?? 'Not available'),
                          leading: const Icon(Icons.business),
                          selected: _contactDisplayOption == 'business_phone',
                          onTap: () {
                            setState(() {
                              _contactDisplayOption = 'business_phone';
                            });
                            // Also update the modal state to refresh the UI
                            setModalState(() {});
                          },
                        ),

                        // Always show Personal Phone option
                        ListTile(
                          title: const Text('Personal Phone'),
                          subtitle: Text(widget.user.phoneNumber ?? 'Not available'),
                          leading: const Icon(Icons.phone),
                          selected: _contactDisplayOption == 'personal_phone',
                          onTap: () {
                            setState(() {
                              _contactDisplayOption = 'personal_phone';
                            });
                            // Also update the modal state to refresh the UI
                            setModalState(() {});
                          },
                        ),

                        // Always show Email option
                        ListTile(
                          title: const Text('Email'),
                          subtitle: Text(widget.user.email ?? 'Not available'),
                          leading: const Icon(Icons.email),
                          selected: _contactDisplayOption == 'email',
                          onTap: () {
                            setState(() {
                              _contactDisplayOption = 'email';
                            });
                            // Also update the modal state to refresh the UI
                            setModalState(() {});
                          },
                        ),

                        // Always show Address option
                        ListTile(
                          title: const Text('Address'),
                          subtitle: Text(_getBusinessAddress() ?? 'Not available'),
                          leading: const Icon(Icons.location_on),
                          selected: _contactDisplayOption == 'address',
                          onTap: () {
                            setState(() {
                              _contactDisplayOption = 'address';
                            });
                            // Also update the modal state to refresh the UI
                            setModalState(() {});
                          },
                        ),
                      ],
                    );
                  },
                ),

                const Divider(),

                // Contact info styling options
                const Padding(
                  padding: EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
                  child: Text(
                    'Contact Info Styling',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Contact text color option
                ListTile(
                  title: const Text('Contact Info Color'),
                  leading: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: _contactTextColor,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey),
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showContactColorPicker();
                  },
                ),

                // Contact text case option
                ListTile(
                  title: const Text('Contact Info Text Case'),
                  subtitle: Text(_getContactTextCaseDescription()),
                  leading: const Icon(Icons.text_format),
                  onTap: () {
                    Navigator.pop(context);
                    _showContactTextCaseOptionsBottomSheet();
                  },
                ),

                // Done button
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        'Done',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final String? position = widget.user.businessProfile?.businessType;
    final String? contactInfo = _getContactInfo();

    return Stack(
      children: [
        // Background overlay
        Container(
          height: 80,
          width: double.infinity,
          color: Colors.black.withAlpha(128),
        ),

        // Left side: Name and position
        Positioned(
          left: 16.0,
          top: 0,
          bottom: 0,
          child: Row(
            children: [
              // User info (name and position)
              SizedBox(
                width: 200, // Adjust width based on user type
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name (tappable only when not for sharing)
                    GestureDetector(
                      onTap: widget.isForSharing ? null : _showNameOptionsBottomSheet,
                      child: Row(
                        children: [
                          Flexible(
                            child: Text(
                              _getDisplayName(),
                              style: TextStyle(
                                color: _nameTextColor,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (!widget.isForSharing) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.edit,
                              size: 14,
                              color: widget.isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70,
                            ),
                          ],
                        ],
                      ),
                    ),

                    // Position
                    if (position != null)
                      Text(
                        position,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                    // Contact info (tappable only when not for sharing)
                    if (contactInfo != null && contactInfo.isNotEmpty)
                      GestureDetector(
                        onTap: widget.isForSharing ? null : _showContactOptionsBottomSheet,
                        child: Row(
                          children: [
                            Icon(
                              _getContactIcon(),
                              color: widget.isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Flexible(
                              child: Text(
                                contactInfo,
                                style: TextStyle(
                                  color: _contactTextColor.withAlpha(204), // Slightly transparent version of the text color
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (!widget.isForSharing) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.edit,
                                size: 12,
                                color: widget.isPremium ? AppTheme.premiumGold.withAlpha(204) : Colors.white70,
                              ),
                            ],
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
