import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';

import '../../../../core/providers/theme_provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../user/domain/entities/user_model.dart';
import '../../../user/domain/entities/party.dart';
import '../../../user/domain/usecases/user_service.dart';
import '../../../user/domain/usecases/party_service.dart';
import '../../../banners/domain/entities/banner_item.dart';
import '../../domain/entities/template_item.dart';

// Import component files
import 'template_card_components/profile_image_handler.dart';
import 'template_card_components/party_components.dart';
import 'template_card_components/user_info_components.dart';
import 'template_card_components/sharing_utilities.dart';
import 'template_card_components/action_buttons.dart';

/// A card widget that displays a template item with optional banner.
class TemplateItemCard extends StatefulWidget {
  /// The template to display
  final TemplateItem template;

  /// The banner to display at the bottom of the template (optional)
  final BannerItem? banner;

  /// The width of the card
  final double width;

  /// The aspect ratio of the template image (width / height)
  final double aspectRatio;

  /// Callback when the template is tapped
  final VoidCallback? onTap;

  /// Callback when the banner is tapped
  final Function(BannerItem)? onBannerTap;

  /// Creates a new template item card
  const TemplateItemCard({
    super.key,
    required this.template,
    this.banner,
    required this.width,
    this.aspectRatio = 0.8, // Default aspect ratio (width / height)
    this.onTap,
    this.onBannerTap,
  });

  @override
  State<TemplateItemCard> createState() => _TemplateItemCardState();
}

class _TemplateItemCardState extends State<TemplateItemCard> {
  UserModel? _user;
  String? _partyLogoUrl;
  List<PartyLeader> _partyLeadership = [];

  // Profile image positioning and scaling
  Offset _profileImageOffset = Offset.zero;
  double _profileImageScale = 1.0;
  final bool _isDraggingProfileImage = false;

  // Screenshot controller for capturing the template
  final ScreenshotController _screenshotController = ScreenshotController();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      debugPrint('TemplateItemCard: Loading user data...');
      final userService = Provider.of<UserService>(context, listen: false);
      final partyService = Provider.of<PartyService>(context, listen: false);
      final user = await userService.getCurrentUser();

      if (mounted) {
        debugPrint('TemplateItemCard: User loaded: ${user?.name}, userType: ${user?.userType}');
        setState(() {
          _user = user;
        });

        // If user is a politician, load their party information
        if (user != null &&
            user.userType == 'politician' &&
            user.politicalProfile != null &&
            user.politicalProfile!.parameterValues != null) {

          debugPrint('TemplateItemCard: User is a politician with profile: ${user.politicalProfile?.parameterValues}');
          // Try to get party_id first, if not available use party_name
          String? partyId = user.politicalProfile!.parameterValues!['party_id'] as String?;

          // If party_id is not available, try to use party_name as the ID
          if (partyId == null || partyId.isEmpty) {
            partyId = user.politicalProfile!.parameterValues!['party_name'] as String?;
            if (partyId != null) {
              // Convert to lowercase for consistency with document IDs
              partyId = partyId.toLowerCase();
            }
          }

          debugPrint('TemplateItemCard: Party ID/name from profile: $partyId');

          if (partyId != null && partyId.isNotEmpty) {
            try {
              debugPrint('TemplateItemCard: Fetching party with ID: $partyId');
              final party = await partyService.getPartyById(partyId);

              if (mounted && party != null) {
                debugPrint('TemplateItemCard: Party loaded: ${party.name}, logo: ${party.logo}');
                debugPrint('TemplateItemCard: Leadership count: ${party.leadership.length}');

                setState(() {
                  _partyLogoUrl = party.logo;
                  _partyLeadership = party.leadership;
                });
              } else {
                debugPrint('TemplateItemCard: Party not found or component unmounted');
              }
            } catch (e) {
              debugPrint('TemplateItemCard: Error loading party information: $e');
            }
          } else {
            debugPrint('TemplateItemCard: No party_id found in political profile');
          }
        } else {
          debugPrint('TemplateItemCard: User is not a politician or missing profile data');
        }
      }
    } catch (e) {
      debugPrint('TemplateItemCard: Error loading user data: $e');
      // Handle error silently
    }
  }

  String? _getProfilePhotoUrl() {
    if (_user == null) {
      debugPrint('TemplateItemCard: User is null');
      return null;
    }

    debugPrint('TemplateItemCard: User type is ${_user!.userType}');

    if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
      // Business user
      debugPrint('TemplateItemCard: User is a businessman');

      // First check if logoUrl is available
      if (_user!.businessProfile!.logoUrl != null && _user!.businessProfile!.logoUrl!.isNotEmpty) {
        debugPrint('TemplateItemCard: Using business logo URL: ${_user!.businessProfile!.logoUrl}');
        return _user!.businessProfile!.logoUrl;
      } else {
        debugPrint('TemplateItemCard: Business logo URL is null or empty');
      }

      // If not, check if there's a logo in the parameter values
      if (_user!.businessProfile!.parameterValues != null) {
        debugPrint('TemplateItemCard: Checking parameter values for logo');
        // Look for any parameter that might contain a logo URL
        for (var entry in _user!.businessProfile!.parameterValues!.entries) {
          debugPrint('TemplateItemCard: Checking parameter ${entry.key}');
          if (entry.key.toLowerCase().contains('logo') &&
              entry.value is String &&
              (entry.value as String).isNotEmpty) {
            debugPrint('TemplateItemCard: Found logo in parameter ${entry.key}: ${entry.value}');
            return entry.value as String;
          }
        }
        debugPrint('TemplateItemCard: No logo found in parameter values');
      } else {
        debugPrint('TemplateItemCard: No parameter values available');
      }

      // If still no logo, use the user's photo as fallback
      if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
        debugPrint('TemplateItemCard: Using user photo as fallback: ${_user!.photoUrl}');
        return _user!.photoUrl;
      } else {
        debugPrint('TemplateItemCard: User photo is null or empty');
      }
    } else if (_user!.userType == 'politician' && _user!.politicalProfile != null) {
      // Political user
      debugPrint('TemplateItemCard: User is a politician');
      final paramValues = _user!.politicalProfile!.parameterValues;
      if (paramValues != null) {
        final photoUrl = paramValues['political_photo'] as String?;
        debugPrint('TemplateItemCard: Political photo URL: $photoUrl');
        return photoUrl;
      } else {
        debugPrint('TemplateItemCard: No political parameter values available');
      }

      // If no political photo, use the user's photo as fallback
      if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
        debugPrint('TemplateItemCard: Using user photo as fallback: ${_user!.photoUrl}');
        return _user!.photoUrl;
      } else {
        debugPrint('TemplateItemCard: User photo is null or empty');
      }
    } else {
      debugPrint('TemplateItemCard: User is neither businessman nor politician or profile is null');
    }

    debugPrint('TemplateItemCard: No profile photo URL found');
    return null;
  }

  @override
  Widget build(BuildContext context) {
    // Calculate height based on MediaQuery height * 0.6 instead of aspect ratio
    final double height = MediaQuery.of(context).size.height * 0.5;
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;
    final String? photoUrl = _getProfilePhotoUrl();

    // Build the main card with template and banner
    Widget mainCard = GestureDetector(
      onTap: _isDraggingProfileImage ? null : widget.onTap, // Disable tap when dragging profile image
      child: Container(
        width: widget.width,
        height: height,
        margin: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          child: Stack(
            children: [
              // Template image
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: widget.template.imageUrl,
                  fit: BoxFit.fill,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.error),
                  ),
                ),
              ),

              // Top banner for business name or political party
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _buildTopBanner(isPremium) ?? const SizedBox.shrink(),
              ),

              // Premium badge if applicable
              if (widget.template.isPremium)
                Positioned(
                  top: 8,
                  left: 8, // Position on the top left to avoid conflict with party logo
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.accentViolet,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'PRO',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

              // Political party logo in top right (if user is a politician)
              if (_user?.userType == 'politician' && _partyLogoUrl != null && _partyLogoUrl!.isNotEmpty)
                Positioned(
                  top: 10, // Move down to avoid conflict with top banner
                  right: 16, // Keep on the right side
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white, width: 2),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(100),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: PartyComponents.buildPartyLogo(_partyLogoUrl) ?? const SizedBox.shrink(),
                  ),
                ),

              // Leadership images on the left side in a row (if user is a politician)
              if (_user?.userType == 'politician' && _partyLeadership.isNotEmpty)
                Positioned(
                  top: 10, // Same vertical position as party logo
                  left: 16, // Position on the left side
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: PartyComponents.buildLeadershipImages(_partyLeadership) ?? const SizedBox.shrink(),
                  ),
                ),

              // Banner at the bottom (if provided)
              if (widget.banner != null)
                Positioned(
                  bottom: 0, // Stick to the bottom
                  left: 0, // Stick to the left
                  right: 0, // Stick to the right
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Banner image with user info overlay
                      Stack(
                        children: [
                          // Banner image (wrapped with GestureDetector for tap)
                          GestureDetector(
                            // Set behavior to deferToChild to ensure profile image's GestureDetector takes precedence
                            behavior: HitTestBehavior.deferToChild,
                            onTap: () {
                              // Only handle banner tap if we're not dragging the profile image
                              if (!_isDraggingProfileImage && widget.onBannerTap != null) {
                                widget.onBannerTap!(widget.banner!);
                              }
                            },
                            child: Stack(
                              children: [
                                SizedBox(
                                  height: 80, // Fixed height for the banner
                                  width: double.infinity,
                                  child: CachedNetworkImage(
                                    imageUrl: widget.banner!.imageUrl,
                                    fit: BoxFit.fill,
                                    placeholder: (context, url) => Container(
                                      color: Colors.grey[200],
                                      child: const Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    ),
                                    errorWidget: (context, url, error) => Container(
                                      color: Colors.grey[200],
                                      child: const Icon(Icons.error),
                                    ),
                                  ),
                                ),

                                // User information overlay
                                UserInfoComponents.buildUserInfoOverlay(_user, isPremium, isForSharing: true),

                                // Add a subtle indicator that the banner is clickable
                                Positioned(
                                  top: 5,
                                  right: 5,
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withAlpha(128), // 0.5 * 255 = 128
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.touch_app,
                                      color: Colors.white,
                                      size: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );

    // Create a column to hold the card and sharing buttons
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Wrap the main card with Screenshot widget for capturing
        Screenshot(
          controller: _screenshotController,
          child: Stack(
            clipBehavior: Clip.none, // Allow content to overflow for better visual connection
            children: [
              mainCard,

          // Helper text when dragging profile image
          if (_isDraggingProfileImage)
            Positioned(
              bottom: 40,
              left: 8,
              right: 8,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withAlpha(180),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Move with one finger, pinch with two fingers to scale',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),

          // Profile photo with draggable functionality
          if (photoUrl != null)
            Positioned(
              // Position the image at the bottom right with padding
              left: 8.0 + widget.width - 64 - 40 + _profileImageOffset.dx, // 40px padding from right
              // Position near the bottom with padding
              top: 8.0 + height - 64 - 40 + _profileImageOffset.dy, // 40px padding from bottom
              child: ProfileImageHandler(
                photoUrl: photoUrl,
                isPremium: isPremium,
                initialOffset: _profileImageOffset,
                initialScale: _profileImageScale,
                onScaleChanged: (scale) {
                  setState(() {
                    _profileImageScale = scale;
                  });
                },
                onOffsetChanged: (offset) {
                  setState(() {
                    _profileImageOffset = offset;
                    _constrainProfileImageOffset();
                  });
                },
              ),
            ),
        ],
      ),
    ),

    // Sharing buttons row - styled to connect with the template
    Transform.translate(
      offset: const Offset(0, -8), // Move up to create overlap effect
      child: ActionButtons.buildSharingButtonsRow(
        context: context,
        isPremium: isPremium,
        onDownload: _downloadTemplate,
        onShareWhatsApp: _shareToWhatsApp,
        onShare: _shareTemplate,
      ),
    ),
  ],
);
  }

  // Method to constrain the profile image offset within the template and banner boundaries
  void _constrainProfileImageOffset() {
    // Calculate the boundaries to keep the image fully within the template and banner
    final imageSize = 64.0; // Size of the profile image
    final height = widget.width / widget.aspectRatio;

    // Calculate the margins to ensure the image stays fully within the card
    // We need to account for the margin of the card (8.0) in our calculations
    final leftBoundary = 8.0; // Left edge of the card (including margin)
    final rightBoundary = 8.0 + widget.width - imageSize; // Right edge minus image width
    final topBoundary = 8.0; // Top edge of the card (including margin)
    final bottomBoundary = 8.0 + height - imageSize; // Bottom of template

    // If banner exists, adjust the bottom boundary to include it
    final bannerHeight = widget.banner != null ? 80.0 : 0.0;
    final absoluteBottomBoundary = bottomBoundary + bannerHeight;

    // Calculate the initial bottom-right position with padding
    final initialX = 8.0 + widget.width - 64 - 40; // 40px padding from right
    final initialY = 8.0 + height - 64 - 40; // 40px padding from bottom

    // Get the absolute position of the image (relative to the card's top-left corner)
    final absoluteX = initialX + _profileImageOffset.dx;
    final absoluteY = initialY + _profileImageOffset.dy;

    // Constrain the absolute position to stay within the boundaries
    final constrainedAbsoluteX = absoluteX.clamp(leftBoundary, rightBoundary);
    final constrainedAbsoluteY = absoluteY.clamp(topBoundary, absoluteBottomBoundary);

    // Convert back to offset from the original center position
    final constrainedX = constrainedAbsoluteX - initialX;
    final constrainedY = constrainedAbsoluteY - initialY;

    // Update the offset with the constrained values
    _profileImageOffset = Offset(constrainedX, constrainedY);
  }



  // Download the template as an image
  void _downloadTemplate() {
    SharingUtilities.downloadTemplate(context, _screenshotController);
  }

  // Share the template to WhatsApp
  void _shareToWhatsApp() {
    SharingUtilities.shareToWhatsApp(context, _screenshotController);
  }

  // Share the template to other apps
  void _shareTemplate() {
    SharingUtilities.shareTemplate(context, _screenshotController);
  }

  // Build a top banner for business name only
  Widget? _buildTopBanner(bool isPremium) {
    if (_user == null) {
      debugPrint('TemplateItemCard: _buildTopBanner - User is null');
      return null;
    }

    String? displayName;
    debugPrint('TemplateItemCard: _buildTopBanner - User type: ${_user!.userType}');

    // Only show top banner for business users with business name
    if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
      displayName = _user!.businessProfile!.businessName;
      debugPrint('TemplateItemCard: _buildTopBanner - Business name: $displayName');

      // If business name is not available, try to get it from parameter values
      if ((displayName == null || displayName.isEmpty) &&
          _user!.businessProfile!.parameterValues != null) {
        debugPrint('TemplateItemCard: _buildTopBanner - Looking for business name in parameter values');

        // Debug: Print all parameter values to see what's available
        _user!.businessProfile!.parameterValues!.forEach((key, value) {
          debugPrint('TemplateItemCard: _buildTopBanner - Parameter $key = $value');
        });

        // Try to find business name in parameters
        final businessNameParam = _user!.businessProfile!.parameterValues!['business_name'] ??
                                 _user!.businessProfile!.parameterValues!['company_name'] ??
                                 _user!.businessProfile!.parameterValues!['organization_name'];

        if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
          displayName = businessNameParam;
          debugPrint('TemplateItemCard: _buildTopBanner - Using business name from parameters: $displayName');
        }
      }
    }
    // Don't show top banner for political users
    else if (_user!.userType == 'politician') {
      debugPrint('TemplateItemCard: _buildTopBanner - User is politician, not showing top banner');
      return null;
    }

    // If no display name found or it's empty, don't show the banner
    if (displayName == null || displayName.isEmpty) {
      debugPrint('TemplateItemCard: _buildTopBanner - No display name found, not showing banner');
      return null;
    }

    // Convert to uppercase
    displayName = displayName.toUpperCase();

    return PartyComponents.buildTopBanner(displayName, isPremium);
  }






}