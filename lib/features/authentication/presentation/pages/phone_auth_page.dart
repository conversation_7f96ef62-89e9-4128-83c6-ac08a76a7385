import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/fancy_card.dart';
import '../../../../core/widgets/gradient_button.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';
import 'otp_verification_page.dart';

class PhoneAuthPage extends StatefulWidget {
  const PhoneAuthPage({super.key});

  @override
  State<PhoneAuthPage> createState() => _PhoneAuthPageState();
}

class _PhoneAuthPageState extends State<PhoneAuthPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  bool _isLoading = false;

  // Rate limiting variables
  DateTime? _lastRequestTime;
  int _requestCount = 0;
  static const int _maxRequestsPerMinute = 2; // Maximum 2 requests per minute

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  // Check if we can send a new request based on rate limiting
  bool _canSendRequest() {
    final now = DateTime.now();

    // If this is the first request or it's been more than a minute since the last request
    if (_lastRequestTime == null || now.difference(_lastRequestTime!).inMinutes >= 1) {
      _lastRequestTime = now;
      _requestCount = 1;
      return true;
    }

    // If we haven't exceeded the maximum requests per minute
    if (_requestCount < _maxRequestsPerMinute) {
      _requestCount++;
      return true;
    }

    // Rate limit exceeded
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppTheme.gradientAppBar(
        title: 'Login with Phone',
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.backgroundWhite,
              AppTheme.lightGradientBg,
            ],
          ),
        ),
        child: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthLoading) {
              setState(() {
                _isLoading = true;
              });
            } else {
              setState(() {
                _isLoading = false;
              });
            }

            if (state is OtpSent) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => OtpVerificationPage(
                    verificationId: state.verificationId,
                    phoneNumber: _phoneController.text,
                  ),
                ),
              );
            } else if (state is AuthFailure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    state.message,
                    style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                  ),
                  backgroundColor: AppTheme.errorRed,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 20),
                      // Logo or App Icon
                      Center(
                        child: Container(
                          height: 100,
                          width: 100,
                          margin: const EdgeInsets.only(bottom: 24),
                          decoration: BoxDecoration(
                            gradient: AppTheme.primaryGradient,
                            shape: BoxShape.circle,
                            boxShadow: AppTheme.lightShadow,
                          ),
                          child: const Icon(
                            Icons.phone_android,
                            size: 50,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      // Title
                      Text(
                        'Enter your phone number',
                        style: AppTheme.headingMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'We\'ll send you a verification code',
                        style: AppTheme.bodyMedium.copyWith(color: AppTheme.secondaryText),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      // Phone input card
                      FancyCard(
                        padding: EdgeInsets.zero,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: TextFormField(
                            controller: _phoneController,
                            keyboardType: TextInputType.phone,
                            style: AppTheme.bodyLarge,
                            decoration: InputDecoration(
                              labelText: 'Phone Number',
                              hintText: '+91 1234567890',
                              helperText: 'Format: +[country code][phone number]',
                              border: InputBorder.none,
                              prefixIcon: const Icon(Icons.phone),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            ),
                            onChanged: (value) {
                              // Ensure the phone number starts with a plus sign
                              if (value.isNotEmpty && !value.startsWith('+')) {
                                _phoneController.text = '+$value';
                                _phoneController.selection = TextSelection.fromPosition(
                                  TextPosition(offset: _phoneController.text.length),
                                );
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your phone number';
                              }
                              // Check if the phone number starts with a plus sign
                              if (!value.startsWith('+')) {
                                return 'Phone number must start with country code (e.g., +91)';
                              }
                              // Check if the phone number contains only digits after the plus sign
                              if (!RegExp(r'^\+\d+$').hasMatch(value)) {
                                return 'Please enter a valid phone number';
                              }
                              return null;
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Help text for users
                      Text(
                        'Make sure to enter your phone number in international format (e.g., +917378880544)',
                        style: AppTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      // Send OTP Button
                      GradientButton(
                        text: 'Send OTP',
                        isLoading: _isLoading,
                        onPressed: () {
                          if (_formKey.currentState!.validate()) {
                            // Check rate limiting
                            if (_canSendRequest()) {
                              // Send OTP directly without showing a dialog
                              context.read<AuthBloc>().add(
                                    SendOtpEvent(
                                      phoneNumber: _phoneController.text.trim(),
                                    ),
                                  );

                              // Show a snackbar to inform the user
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Sending OTP to ${_phoneController.text.trim()}...',
                                    style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                                  ),
                                  duration: const Duration(seconds: 2),
                                  backgroundColor: AppTheme.primaryBlue,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              );
                            } else {
                              // Show rate limit exceeded message
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Too many requests. Please wait a moment before trying again.',
                                    style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                                  ),
                                  duration: const Duration(seconds: 3),
                                  backgroundColor: AppTheme.errorRed,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              );
                            }
                          }
                        },
                      ),
                      const SizedBox(height: 24),
                      // App branding
                      Text(
                        'QuickPosters',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.secondaryText,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
