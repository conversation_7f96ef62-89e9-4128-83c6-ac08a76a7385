import Flutter
import UIKit
import Photos

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
    let imageSaverChannel = FlutterMethodChannel(name: "com.quickposters/image_saver", binaryMessenger: controller.binaryMessenger)

    imageSaverChannel.setMethodCallHandler({
      (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
      if call.method == "saveImageToGallery" {
        guard let args = call.arguments as? [String: Any],
              let filePath = args["filePath"] as? String else {
          result(false)
          return
        }

        self.saveImageToGallery(filePath: filePath, result: result)
      } else {
        result(FlutterMethodNotImplemented)
      }
    })

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  private func saveImageToGallery(filePath: String, result: @escaping FlutterResult) {
    guard let image = UIImage(contentsOfFile: filePath) else {
      result(false)
      return
    }

    PHPhotoLibrary.shared().performChanges({
      PHAssetChangeRequest.creationRequestForAsset(from: image)
    }, completionHandler: { success, error in
      DispatchQueue.main.async {
        if success {
          result(true)
        } else {
          print("Error saving image to gallery: \(String(describing: error))")
          result(false)
        }
      }
    })
  }
}
